# Saga 分布式事务系统 Dockerfile
# 多阶段构建，减小镜像大小

# 构建阶段
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置 Go 代理（加速依赖下载）
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.org
ENV CGO_ENABLED=0
ENV GOOS=linux

# 安装必要的工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖（利用 Docker 缓存层）
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN go build \
    -ldflags="-w -s -X main.Version=docker \
              -X main.BuildTime=$(date +%Y-%m-%d_%H:%M:%S) \
              -X main.GitCommit=docker-build" \
    -o saga main.go

# 运行阶段
FROM alpine:latest

# 安装必要的运行时依赖
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非 root 用户
RUN addgroup -g 1001 -S saga && \
    adduser -u 1001 -S saga -G saga

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/saga .

# 复制配置文件
COPY --from=builder /app/manifest ./manifest
COPY --from=builder /app/resource ./resource

# 更改文件所有者
RUN chown -R saga:saga /app

# 切换到非 root 用户
USER saga

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./saga"]
