FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata wget
ENV TZ=Asia/Shanghai
RUN addgroup -g 1001 -S saga && adduser -u 1001 -S saga -G saga
WORKDIR /app
COPY bin/saga ./saga
COPY manifest ./manifest
COPY resource ./resource
RUN chown -R saga:saga /app && chmod +x ./saga
USER saga
EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1
CMD ["./saga"]
