# 简化的 Dockerfile - 使用预构建的二进制文件
FROM alpine:latest

# 安装必要的工具
RUN apk --no-cache add ca-certificates tzdata

# 创建用户
RUN addgroup -g 1001 -S saga && \
    adduser -u 1001 -S saga -G saga

# 设置工作目录
WORKDIR /app

# 复制预构建的二进制文件
COPY saga /app/saga
COPY manifest /app/manifest

# 设置权限
RUN chmod +x /app/saga && \
    chown -R saga:saga /app

# 切换到非 root 用户
USER saga

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/hello || exit 1

# 启动命令
CMD ["./saga"]
