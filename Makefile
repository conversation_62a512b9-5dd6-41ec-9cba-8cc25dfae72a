# Saga 分布式事务系统 Makefile
# 项目配置
ROOT_DIR    = $(shell pwd)
PROJECT_NAME = saga
BINARY_NAME = saga
DOCKER_NAME = saga-transaction-system
NAMESPACE   = "default"
DEPLOY_NAME = "saga-service"

# Go 相关配置
GO_VERSION = 1.21.0
GOOS       = $(shell go env GOOS)
GOARCH     = $(shell go env GOARCH)
CGO_ENABLED = 0

# 版本信息
VERSION    = $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT) -w -s"

# 默认目标
.DEFAULT_GOAL := help

# 包含 GoFrame 相关的 Makefile
include ./hack/hack-cli.mk
include ./hack/hack.mk

# =============================================================================
# 帮助信息
# =============================================================================

.PHONY: help
help: ## 显示帮助信息
	@echo "Saga 分布式事务系统 - 可用命令:"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "GoFrame 相关命令:"
	@echo "  build              使用 GoFrame CLI 构建项目"
	@echo "  dao                生成 DAO/DO/Entity 文件"
	@echo "  ctrl               生成控制器文件"
	@echo "  service            生成服务文件"
	@echo "  up                 更新 GoFrame 到最新版本"
	@echo ""
	@echo "资源配置说明:"
	@echo "  性能测试配置: 数据库(4核16GB) + 应用(8核16GB)"
	@echo "  轻量级配置:   数据库(2核4GB)  + 应用(4核8GB)"

# =============================================================================
# 开发环境
# =============================================================================

.PHONY: dev
dev: ## 启动开发环境
	@echo "启动 Saga 开发环境..."
	@GOMAXPROCS=8 GOGC=100 go run main.go

.PHONY: dev-watch
dev-watch: cli.install ## 启动开发环境（文件监控）
	@echo "启动 Saga 开发环境（文件监控）..."
	@GOMAXPROCS=8 GOGC=100 gf run main.go --args web

.PHONY: dev-perf
dev-perf: ## 启动开发环境（性能测试优化配置）
	@echo "启动 Saga 开发环境（性能测试配置）..."
	@echo "Go运行时配置: GOMAXPROCS=8, GOGC=50, 内存限制=16GB"
	@GOMAXPROCS=8 GOGC=50 GOMEMLIMIT=16GiB go run main.go

.PHONY: dev-light
dev-light: ## 启动开发环境（轻量配置）
	@echo "启动 Saga 开发环境（轻量配置）..."
	@echo "Go运行时配置: GOMAXPROCS=4, GOGC=100, 内存限制=8GB"
	@GOMAXPROCS=4 GOGC=100 GOMEMLIMIT=8GiB go run main.go

.PHONY: deps
deps: ## 下载依赖
	@echo "下载 Go 依赖..."
	@go mod download
	@go mod tidy

.PHONY: deps-update
deps-update: ## 更新依赖
	@echo "更新 Go 依赖..."
	@go get -u ./...
	@go mod tidy

# =============================================================================
# 构建
# =============================================================================

.PHONY: build-local
build-local: ## 构建本地二进制文件
	@echo "构建本地二进制文件..."
	@echo "目标架构: $(GOOS)/$(GOARCH)"
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=$(GOOS) GOARCH=$(GOARCH) go build $(LDFLAGS) -o bin/$(BINARY_NAME) main.go
	@echo "构建完成: bin/$(BINARY_NAME)"
	@file bin/$(BINARY_NAME)

.PHONY: build-linux
build-linux: ## 构建 Linux 二进制文件
	@echo "构建 Linux 二进制文件..."
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-linux-amd64 main.go
	@echo "构建完成: bin/$(BINARY_NAME)-linux-amd64"

.PHONY: build-darwin
build-darwin: ## 构建 macOS 二进制文件
	@echo "构建 macOS 二进制文件..."
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-darwin-amd64 main.go
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-darwin-arm64 main.go
	@echo "构建完成: bin/$(BINARY_NAME)-darwin-*"

.PHONY: build-windows
build-windows: ## 构建 Windows 二进制文件
	@echo "构建 Windows 二进制文件..."
	@CGO_ENABLED=$(CGO_ENABLED) GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-windows-amd64.exe main.go
	@echo "构建完成: bin/$(BINARY_NAME)-windows-amd64.exe"

.PHONY: build-all
build-all: build-local build-linux build-darwin build-windows ## 构建所有平台的二进制文件

.PHONY: clean
clean: ## 清理构建文件
	@echo "清理构建文件..."
	@rm -rf bin/
	@rm -rf temp/
	@rm -rf dist/
	@echo "清理完成"

# =============================================================================
# 测试
# =============================================================================

.PHONY: test
test: ## 运行所有测试
	@echo "运行所有测试..."
	@go test -v ./...

.PHONY: test-coverage
test-coverage: ## 运行测试并生成覆盖率报告
	@echo "运行测试并生成覆盖率报告..."
	@go test -v -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "覆盖率报告已生成: coverage.html"

.PHONY: test-race
test-race: ## 运行竞态检测测试
	@echo "运行竞态检测测试..."
	@go test -v -race ./...

.PHONY: test-service
test-service: ## 运行服务层测试
	@echo "运行服务层测试..."
	@go test -v ./internal/service/...

.PHONY: test-dao
test-dao: ## 运行 DAO 层测试
	@echo "运行 DAO 层测试..."
	@go test -v ./internal/dao/...

.PHONY: benchmark
benchmark: ## 运行性能测试
	@echo "运行性能测试..."
	@go test -v -bench=. -benchmem ./...

# =============================================================================
# 代码质量
# =============================================================================

.PHONY: fmt
fmt: ## 格式化代码
	@echo "格式化代码..."
	@go fmt ./...
	@gofmt -s -w .

.PHONY: vet
vet: ## 运行 go vet
	@echo "运行 go vet..."
	@go vet ./...

.PHONY: lint
lint: ## 运行 golangci-lint
	@echo "运行 golangci-lint..."
	@which golangci-lint > /dev/null || (echo "请先安装 golangci-lint: brew install golangci-lint" && exit 1)
	@golangci-lint run

.PHONY: check
check: fmt vet lint test ## 运行所有代码检查

# =============================================================================
# Docker
# =============================================================================

.PHONY: docker-build
docker-build: ## 构建 Docker 镜像
	@echo "构建 Docker 镜像..."
	@docker build --network=host --progress=plain -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-build-fast
docker-build-fast: ## 快速构建 Docker 镜像（使用缓存）
	@echo "快速构建 Docker 镜像（使用缓存）..."
	@docker build --network=host --cache-from $(DOCKER_NAME):latest -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-build-no-cache
docker-build-no-cache: ## 构建 Docker 镜像（不使用缓存）
	@echo "构建 Docker 镜像（不使用缓存）..."
	@docker build --network=host --no-cache --progress=plain -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-build-china
docker-build-china: ## 使用国内镜像源构建 Docker 镜像
	@echo "使用国内镜像源构建 Docker 镜像..."
	@docker build --network=host --progress=plain -f Dockerfile.china -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest .
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: build-docker-binary
build-docker-binary: ## 构建 Docker 专用的二进制文件
	@echo "构建 Docker 专用的二进制文件（Linux ARM64）..."
	@CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build $(LDFLAGS) -o bin/$(BINARY_NAME)-docker main.go
	@echo "构建完成: bin/$(BINARY_NAME)-docker"
	@file bin/$(BINARY_NAME)-docker

.PHONY: docker-build-local
docker-build-local: build-docker-binary ## 使用本地二进制文件构建 Docker 镜像
	@echo "使用本地二进制文件构建 Docker 镜像..."
	@echo "检查二进制文件..."
	@ls -la bin/saga-docker
	@echo "创建临时构建目录..."
	@mkdir -p temp/docker-build
	@cp bin/saga-docker temp/docker-build/saga
	@cp -r manifest temp/docker-build/ 2>/dev/null || echo "manifest 目录不存在，跳过"
	@cp -r resource temp/docker-build/ 2>/dev/null || echo "resource 目录不存在，跳过"
	@echo "创建临时 Dockerfile..."
	@echo "FROM alpine:latest" > temp/docker-build/Dockerfile
	@echo "RUN apk --no-cache add ca-certificates tzdata wget" >> temp/docker-build/Dockerfile
	@echo "ENV TZ=Asia/Shanghai" >> temp/docker-build/Dockerfile
	@echo "RUN addgroup -g 1001 -S saga && adduser -u 1001 -S saga -G saga" >> temp/docker-build/Dockerfile
	@echo "WORKDIR /app" >> temp/docker-build/Dockerfile
	@echo "COPY saga ./saga" >> temp/docker-build/Dockerfile
	@echo "COPY manifest ./manifest" >> temp/docker-build/Dockerfile
	@echo "COPY resource ./resource" >> temp/docker-build/Dockerfile
	@echo "RUN chown -R saga:saga /app && chmod +x ./saga" >> temp/docker-build/Dockerfile
	@echo "USER saga" >> temp/docker-build/Dockerfile
	@echo "EXPOSE 8080" >> temp/docker-build/Dockerfile
	@echo "HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1" >> temp/docker-build/Dockerfile
	@echo "CMD [\"./saga\"]" >> temp/docker-build/Dockerfile
	@echo "开始构建 Docker 镜像..."
	@docker build --no-cache -f temp/docker-build/Dockerfile -t $(DOCKER_NAME):$(VERSION) -t $(DOCKER_NAME):latest temp/docker-build/
	@rm -rf temp/docker-build
	@echo "Docker 镜像构建完成: $(DOCKER_NAME):$(VERSION)"

.PHONY: docker-run
docker-run: ## 运行 Docker 容器（性能测试配置）
	@echo "运行 Docker 容器（CPU: 8核, 内存: 16GB）..."
	@docker run --rm \
		--cpus="8.0" \
		--memory="16g" \
		--memory-swap="16g" \
		-p 8080:8080 \
		--name saga-container \
		-e GOMAXPROCS=8 \
		-e GOGC=100 \
		--link mysql:mysql \
		$(DOCKER_NAME):latest
	@echo "应用容器已启动，端口: 8080"
	@echo "资源限制: CPU=8核, 内存=16GB"

.PHONY: docker-run-light
docker-run-light: ## 运行 Docker 容器（轻量配置）
	@echo "运行 Docker 容器（CPU: 4核, 内存: 8GB）..."
	@docker run --rm \
		--cpus="4.0" \
		--memory="8g" \
		--memory-swap="8g" \
		-p 8080:8080 \
		--name saga-container \
		-e GOMAXPROCS=4 \
		-e GOGC=100 \
		--link mysql:mysql \
		$(DOCKER_NAME):latest
	@echo "应用容器已启动，端口: 8080"
	@echo "资源限制: CPU=4核, 内存=8GB（轻量配置）"

.PHONY: docker-push
docker-push: docker-build ## 推送 Docker 镜像
	@echo "推送 Docker 镜像..."
	@docker push $(DOCKER_NAME):$(VERSION)
	@docker push $(DOCKER_NAME):latest

.PHONY: docker-clean
docker-clean: ## 清理 Docker 镜像和容器
	@echo "清理 Docker 镜像和容器..."
	@docker container prune -f
	@docker image prune -f
	@docker rmi $(DOCKER_NAME):latest $(DOCKER_NAME):$(VERSION) 2>/dev/null || true

.PHONY: docker-fix
docker-fix: ## 诊断和修复 Docker 网络问题
	@echo "运行 Docker 网络诊断..."
	@bash scripts/docker-fix.sh

.PHONY: docker-test
docker-test: ## 测试 Docker 环境
	@echo "测试 Docker 环境..."
	@docker --version
	@docker info | head -10
	@echo "测试镜像拉取..."
	@docker pull hello-world:latest
	@docker run --rm hello-world
	@docker rmi hello-world:latest

# =============================================================================
# Docker Compose
# =============================================================================

.PHONY: compose-up
compose-up: ## 启动 Docker Compose 环境（性能测试配置）
	@echo "启动 Docker Compose 环境（性能测试配置）..."
	@docker-compose up -d
	@echo "等待服务启动..."
	@sleep 10
	@make status

.PHONY: compose-up-light
compose-up-light: ## 启动 Docker Compose 环境（轻量配置）
	@echo "启动 Docker Compose 环境（轻量配置）..."
	@docker-compose -f docker-compose.light.yml up -d
	@echo "等待服务启动..."
	@sleep 10
	@make status

.PHONY: compose-down
compose-down: ## 停止 Docker Compose 环境
	@echo "停止 Docker Compose 环境..."
	@docker-compose down
	@docker-compose -f docker-compose.light.yml down 2>/dev/null || true

.PHONY: compose-logs
compose-logs: ## 查看 Docker Compose 日志
	@docker-compose logs -f

.PHONY: compose-restart
compose-restart: compose-down compose-up ## 重启 Docker Compose 环境

.PHONY: compose-restart-light
compose-restart-light: compose-down compose-up-light ## 重启 Docker Compose 环境（轻量配置）

# =============================================================================
# 数据库
# =============================================================================

.PHONY: db-start
db-start: ## 启动 MySQL 数据库容器（性能测试配置）
	@echo "启动 MySQL 数据库容器（CPU: 4核, 内存: 16GB）..."
	@docker run -d \
		--name mysql \
		--cpus="4.0" \
		--memory="16g" \
		--memory-swap="16g" \
		-p 3306:3306 \
		-e MYSQL_ROOT_PASSWORD=12345678a \
		-e MYSQL_DATABASE=saga \
		-e MYSQL_INNODB_BUFFER_POOL_SIZE=12G \
		-e MYSQL_INNODB_LOG_FILE_SIZE=2G \
		-e MYSQL_MAX_CONNECTIONS=500 \
		-e MYSQL_INNODB_FLUSH_LOG_AT_TRX_COMMIT=2 \
		-v mysql_data:/var/lib/mysql \
		mysql:8.0 \
		--innodb-buffer-pool-size=12G \
		--innodb-redo-log-capacity=2G \
		--max-connections=500 \
		--innodb-flush-log-at-trx-commit=2 \
		--innodb-flush-method=O_DIRECT
	@echo "MySQL 数据库已启动，端口: 3306"
	@echo "资源限制: CPU=4核, 内存=16GB"

.PHONY: db-start-light
db-start-light: ## 启动 MySQL 数据库容器（轻量配置）
	@echo "启动 MySQL 数据库容器（CPU: 2核, 内存: 4GB）..."
	@docker run -d \
		--name mysql \
		--cpus="2.0" \
		--memory="4g" \
		--memory-swap="4g" \
		-p 3306:3306 \
		-e MYSQL_ROOT_PASSWORD=12345678a \
		-e MYSQL_DATABASE=saga \
		-e MYSQL_INNODB_BUFFER_POOL_SIZE=2G \
		-e MYSQL_MAX_CONNECTIONS=200 \
		-v mysql_data:/var/lib/mysql \
		-v $(ROOT_DIR)/manifest/config/mysql/light.cnf:/etc/mysql/conf.d/light.cnf \
		mysql:8.0 \
		--innodb-buffer-pool-size=2G \
		--innodb-redo-log-capacity=512M \
		--max-connections=200
	@echo "MySQL 数据库已启动，端口: 3306"
	@echo "资源限制: CPU=2核, 内存=4GB（轻量配置）"

.PHONY: db-start-simple
db-start-simple: ## 启动 MySQL 数据库容器（简单配置，兼容性最好）
	@echo "启动 MySQL 数据库容器（简单配置）..."
	@docker run -d \
		--name mysql \
		--cpus="2.0" \
		--memory="4g" \
		--memory-swap="4g" \
		-p 3306:3306 \
		-e MYSQL_ROOT_PASSWORD=12345678a \
		-e MYSQL_DATABASE=saga \
		-v mysql_data:/var/lib/mysql \
		mysql:8.0
	@echo "MySQL 数据库已启动，端口: 3306"
	@echo "资源限制: CPU=2核, 内存=4GB（简单配置）"

.PHONY: db-stop
db-stop: ## 停止 MySQL 数据库容器
	@echo "停止 MySQL 数据库容器..."
	@docker stop mysql || true
	@docker rm mysql || true

.PHONY: db-logs
db-logs: ## 查看数据库日志
	@docker logs -f mysql

.PHONY: db-shell
db-shell: ## 连接到数据库 shell
	@docker exec -it mysql mysql -uroot -p12345678a saga

.PHONY: db-init
db-init: ## 初始化数据库表结构
	@echo "初始化数据库表结构..."
	@docker exec -i mysql mysql -uroot -p12345678a saga < manifest/config/sql/tables.sql
	@echo "数据库表结构初始化完成"

.PHONY: db-reset
db-reset: db-stop db-start ## 重置数据库
	@echo "等待数据库启动..."
	@sleep 10
	@make db-init



.PHONY: db-quick-test
db-quick-test: ## 生成快速测试数据（100条，用于开发验证）
	@echo "生成快速测试数据..."
	@docker exec -i mysql mysql -uroot -p12345678a saga < scripts/quick-test-data.sql
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL GenerateQuickTestData();"

.PHONY: db-init-perf-data
db-init-perf-data: ## 初始化性能测试数据脚本
	@echo "初始化性能测试数据脚本..."
	@docker exec -i mysql mysql -uroot -p12345678a saga < scripts/simple-perf-data.sql
	@echo "✅ 性能测试数据脚本初始化完成"

.PHONY: db-generate-level1
db-generate-level1: ## 生成 Level 1 测试数据（10万条）
	@echo "生成 Level 1 测试数据（10万 Saga 事务）..."
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL GenerateLevel1DataSimple();"

.PHONY: db-generate-level2
db-generate-level2: ## 生成 Level 2 测试数据（100万条）
	@echo "生成 Level 2 测试数据（100万 Saga 事务）..."
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL GenerateLevel2DataSimple();"

.PHONY: db-generate-level3
db-generate-level3: ## 生成 Level 3 测试数据（1000万条）
	@echo "生成 Level 3 测试数据（1000万 Saga 事务）..."
	@echo "⚠️  警告：此操作可能需要2-4小时，请确保有足够的磁盘空间"
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL GenerateLevel3DataSimple();"

.PHONY: db-generate-custom
db-generate-custom: ## 生成自定义数量的测试数据（使用 COUNT=数量）
	@echo "生成自定义数量的测试数据..."
	@if [ "$(COUNT)" = "" ]; then echo "请指定数量: make db-generate-custom COUNT=50000"; exit 1; fi
	@echo "生成 $(COUNT) 条 Saga 事务数据..."
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL GenerateSimplePerfData($(COUNT));"

.PHONY: db-clean-test-data
db-clean-test-data: ## 清理所有测试数据
	@echo "清理所有测试数据..."
	@docker exec -i mysql mysql -uroot -p12345678a saga < scripts/simple-perf-data.sql
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL CleanTestData();"

.PHONY: db-show-stats
db-show-stats: ## 显示数据统计信息
	@echo "显示数据统计信息..."
	@docker exec -i mysql mysql -uroot -p12345678a saga < scripts/simple-perf-data.sql
	@docker exec -i mysql mysql -uroot -p12345678a saga -e "CALL ShowDataStatistics();"

# =============================================================================
# 性能测试
# =============================================================================

.PHONY: perf-install
perf-install: ## 安装性能测试工具
	@echo "安装性能测试工具..."
	@which brew > /dev/null || (echo "请先安装 Homebrew" && exit 1)
	@brew install wrk jmeter || true
	@go install github.com/rakyll/hey@latest

.PHONY: perf-test-basic
perf-test-basic: ## 运行基础性能测试
	@echo "运行基础性能测试..."
	@echo "测试 Saga 创建接口..."
	@wrk -t4 -c100 -d30s --script=scripts/perf/create_saga.lua http://localhost:8080/api/saga/create

.PHONY: perf-test-query
perf-test-query: ## 运行查询性能测试
	@echo "运行查询性能测试..."
	@hey -n 10000 -c 100 http://localhost:8080/api/saga/status/test-saga-001

# =============================================================================
# 部署
# =============================================================================

.PHONY: install
install: build-local ## 安装到本地
	@echo "安装到本地..."
	@sudo cp bin/$(BINARY_NAME) /usr/local/bin/
	@echo "安装完成: /usr/local/bin/$(BINARY_NAME)"

.PHONY: uninstall
uninstall: ## 从本地卸载
	@echo "从本地卸载..."
	@sudo rm -f /usr/local/bin/$(BINARY_NAME)
	@echo "卸载完成"

# =============================================================================
# 开发工具
# =============================================================================

.PHONY: tools
tools: ## 安装开发工具
	@echo "安装开发工具..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/rakyll/hey@latest
	@echo "开发工具安装完成"

.PHONY: mod-graph
mod-graph: ## 显示模块依赖图
	@go mod graph

.PHONY: mod-why
mod-why: ## 解释为什么需要某个模块
	@echo "使用方法: make mod-why MODULE=github.com/example/module"
	@if [ "$(MODULE)" != "" ]; then go mod why $(MODULE); fi

.PHONY: version
version: ## 显示版本信息
	@echo "项目: $(PROJECT_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Git提交: $(GIT_COMMIT)"
	@echo "Go版本: $(shell go version)"

# =============================================================================
# 快捷命令
# =============================================================================

.PHONY: start
start: db-start dev-perf ## 启动完整开发环境（性能测试配置：数据库4核16GB + 应用8核16GB）

.PHONY: start-light
start-light: db-start-light dev-light ## 启动完整开发环境（轻量配置：数据库2核4GB + 应用4核8GB）

.PHONY: start-docker
start-docker: db-start docker-run ## 启动 Docker 环境（性能测试配置）

.PHONY: start-docker-light
start-docker-light: db-start-light docker-run-light ## 启动 Docker 环境（轻量配置）

.PHONY: start-compose
start-compose: compose-up ## 启动 Docker Compose 环境（性能测试配置）

.PHONY: start-compose-light
start-compose-light: compose-up-light ## 启动 Docker Compose 环境（轻量配置）

.PHONY: stop
stop: ## 停止所有环境
	@echo "停止所有环境..."
	@make db-stop 2>/dev/null || true
	@docker stop saga-container 2>/dev/null || true
	@make compose-down 2>/dev/null || true

.PHONY: restart
restart: stop start ## 重启开发环境（性能测试配置）

.PHONY: restart-light
restart-light: stop start-light ## 重启开发环境（轻量配置）

.PHONY: logs
logs: db-logs ## 查看日志

.PHONY: status
status: ## 查看服务状态
	@echo "检查服务状态..."
	@docker ps | grep -E "(mysql|saga)" || echo "没有运行的容器"
	@curl -s http://localhost:8080/health 2>/dev/null && echo "应用服务正常" || echo "应用服务未响应"

.PHONY: monitor
monitor: ## 监控容器资源使用情况
	@echo "监控容器资源使用情况..."
	@echo "=== Docker 容器状态 ==="
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" 2>/dev/null || echo "没有运行的容器"
	@echo ""
	@echo "=== 系统资源使用情况 ==="
	@echo "CPU 使用率:"
	@top -l 1 -n 0 | grep "CPU usage" || echo "无法获取 CPU 信息"
	@echo ""
	@echo "内存使用情况:"
	@vm_stat | head -5
	@echo ""
	@echo "磁盘使用情况:"
	@df -h / | tail -1

.PHONY: monitor-live
monitor-live: ## 实时监控容器资源使用情况
	@echo "实时监控容器资源使用情况（按 Ctrl+C 退出）..."
	@docker stats