# Saga 分布式事务系统性能压测计划

> **文档更新**: 2025年7月31日 - 已根据 `docs/saga-transactions-api.md` 更新所有API接口路径和参数

## 🎯 测试目标

### 主要目标
- 评估系统在不同数据量级下的性能表现
- 识别系统性能瓶颈和稳定性问题
- 为系统优化提供数据支撑
- 验证系统在高并发场景下的可靠性

### 关键指标
- **吞吐量（TPS）**：每秒处理的事务数
- **响应时间**：P50、P95、P99 响应时间
- **错误率**：请求失败率
- **资源使用率**：CPU、内存、磁盘 I/O、网络 I/O
- **数据库性能**：连接数、查询响应时间、锁等待

## 🔧 测试环境准备

### 硬件配置
```yaml
当前测试环境 (MacBook Pro M3 Max):
  CPU: Apple M3 Max (14核: 10性能核 + 4效率核)
  内存: 36GB 统一内存
  磁盘: SSD 926GB (可用 884GB)
  网络: 千兆以太网 + Wi-Fi 6E
  操作系统: macOS 15.5

注意:
  - 由于是单机环境，应用服务器和数据库服务器运行在同一台机器上
  - 压测客户端也在同一台机器上，需要考虑资源竞争
  - 建议为不同组件分配合理的资源限制

资源分配建议:
  应用服务器: 8核 + 16GB内存
  数据库服务器: 4核 + 16GB内存
  压测客户端: 2核 + 4GB内存
```

### 软件环境
```yaml
操作系统: macOS 15.5 (24F74)
Go版本: 1.21+ (建议使用最新版本)
数据库: MySQL 8.0+ (通过Docker运行)
压测工具:
  - JMeter 5.5+ (可通过Homebrew安装)
  - wrk (brew install wrk)
  - hey (go install github.com/rakyll/hey@latest)
  - 自定义Go压测脚本
监控工具:
  - Activity Monitor (系统自带)
  - Docker Desktop 监控
  - MySQL Performance Schema
  - top/iostat/netstat (macOS版本)
  - 可选: Prometheus + Grafana (Docker部署)

安装命令:
  brew install jmeter wrk
  go install github.com/rakyll/hey@latest

macOS环境特殊配置:
  # 增加文件描述符限制
  ulimit -n 65536

  # 调整网络参数 (需要重启生效)
  sudo sysctl -w kern.ipc.somaxconn=1024
  sudo sysctl -w net.inet.tcp.msl=1000

  # Docker资源限制调整
  # 在Docker Desktop设置中分配: CPU 8核, 内存 20GB
```

## 📊 测试数据分级

### Level 1: 10万级（基础压测）
```yaml
数据规模:
  - Saga事务: 100,000条
  - 步骤记录: 300,000条（平均每个Saga 3个步骤）
  - 并发用户: 50-200 (单机环境调整)
  - 测试时长: 30分钟

目标指标 (单机环境):
  - TPS: ≥ 500 (考虑单机资源限制)
  - P95响应时间: ≤ 800ms
  - 错误率: ≤ 0.5%
  - 总CPU使用率: ≤ 80% (包含所有组件)
  - 总内存使用率: ≤ 85%
```

### Level 2: 100万级（中等压测）
```yaml
数据规模:
  - Saga事务: 1,000,000条
  - 步骤记录: 3,000,000条
  - 并发用户: 200-800 (单机环境调整)
  - 测试时长: 60分钟

目标指标 (单机环境):
  - TPS: ≥ 800 (考虑单机资源限制)
  - P95响应时间: ≤ 1,500ms
  - 错误率: ≤ 1%
  - 总CPU使用率: ≤ 85%
  - 总内存使用率: ≤ 90%
```

### Level 3: 1000万级（极限压测）
```yaml
数据规模:
  - Saga事务: 10,000,000条
  - 步骤记录: 30,000,000条
  - 并发用户: 300-1,000 (单机环境调整)
  - 测试时长: 120分钟

目标指标 (单机环境):
  - TPS: ≥ 1,000 (考虑单机资源限制)
  - P95响应时间: ≤ 3,000ms
  - 错误率: ≤ 2%
  - 总CPU使用率: ≤ 90%
  - 总内存使用率: ≤ 95%

注意: 此级别测试可能需要调整JVM/Go运行时参数以优化内存使用
```

## 🎯 核心接口压测场景

> **接口更新说明**: 本章节的API接口已根据 `docs/saga-transactions-api.md` 文档进行更新，确保与实际实现保持一致。

### ⚠️ 重要测试约束

**数据依赖关系**：
1. **事务创建接口** - 独立测试，无依赖
2. **补偿上报接口** - 必须使用创建接口返回的真实 sagaId
3. **状态查询接口** - 必须使用创建接口返回的真实 sagaId
4. **事务提交接口** - 必须使用创建接口返回的真实 sagaId，且状态为 running
5. **事务回滚接口** - 必须使用创建接口返回的真实 sagaId

**测试顺序要求**：
1. 先执行事务创建测试，收集真实的 sagaId
2. 再执行其他接口测试，使用收集到的 sagaId
3. 不能使用随机生成或硬编码的 sagaId

### 4.1 Saga事务创建接口
```http
POST /saga/transactions
Content-Type: application/json

{
  "name": "订单处理流程",
  "stepIndexMode": "auto"
}
```

**压测参数：**
- 并发数：100, 500, 1000, 2000, 5000
- 持续时间：每个并发级别测试10分钟
- 数据变化：每次请求使用不同的事务名称

### 4.2 补偿上报接口
```http
POST /saga/transactions/compensation
Content-Type: application/json

{
  "sagaId": "{{sagaId}}",
  "action": "CreateOrder",
  "serviceName": "order-service",
  "contextData": "{\"orderId\":\"12345\",\"userId\":\"user123\"}",
  "compensationContext": "{\"orderId\":\"12345\",\"reason\":\"cancel_order\"}",
  "compensateEndpoint": "http://order-service:8080/orders/compensate"
}
```

**⚠️ 重要约束条件：**
- **sagaId 必须真实存在**：只能使用通过创建事务接口获取的真实 sagaId
- **不能随意生成**：使用不存在的 sagaId 会导致测试失败
- **数据依赖关系**：补偿上报测试必须依赖于事务创建测试的结果

**压测参数：**
- 并发数：200, 1000, 2000, 3000, 6000
- 持续时间：每个并发级别测试15分钟
- 数据依赖：需要先创建Saga事务并获取真实的sagaId列表

### 4.3 事务提交接口
```http
POST /saga/transactions/commit
Content-Type: application/json

{
  "sagaId": "{{sagaId}}"
}
```

**⚠️ 重要约束条件：**
- **sagaId 必须真实存在**：只能提交通过创建事务接口获取的真实 sagaId
- **状态约束**：只有 running 状态的事务才能提交
- **数据依赖关系**：提交测试必须基于已创建且状态正确的saga事务

**压测参数：**
- 并发数：100, 300, 500, 1000, 2000
- 持续时间：每个并发级别测试10分钟
- 前置条件：需要有running状态的Saga事务

### 4.4 状态查询接口
```http
GET /saga/transactions/{{sagaId}}
```

**⚠️ 重要约束条件：**
- **sagaId 必须真实存在**：只能查询通过创建事务接口获取的真实 sagaId
- **数据依赖关系**：查询测试必须基于已创建的saga事务

**压测参数：**
- 并发数：500, 2000, 5000, 10000
- 持续时间：每个并发级别测试5分钟
- 查询模式：随机查询已存在的sagaId

### 4.5 事务回滚接口
```http
POST /saga/transactions/rollback
Content-Type: application/json

{
  "sagaId": "{{sagaId}}",
  "failReason": "支付服务异常",
  "failedStep": "ProcessPayment",
  "executionMode": "sync"
}
```

**⚠️ 重要约束条件：**
- **sagaId 必须真实存在**：只能回滚通过创建事务接口获取的真实 sagaId
- **状态约束**：只有特定状态的事务才能回滚
- **数据依赖关系**：回滚测试必须基于已创建的saga事务

**压测参数：**
- 并发数：200, 800, 1500, 3000
- 持续时间：每个并发级别测试8分钟

## 📈 压测执行计划

### Phase 1: 基础功能压测（10万级）
```yaml
Week 1:
  Day 1-2: 环境搭建和数据准备
  Day 3: Saga创建接口压测
  Day 4: 步骤上报接口压测
  Day 5: 补偿执行接口压测
  Day 6: 状态查询接口压测
  Day 7: 数据分析和报告
```

### Phase 2: 中等规模压测（100万级）
```yaml
Week 2:
  Day 1: 数据扩容和环境调优
  Day 2-3: 核心接口压测
  Day 4: 混合场景压测
  Day 5: 长时间稳定性测试
  Day 6-7: 性能分析和优化建议
```

### Phase 3: 极限压测（1000万级）
```yaml
Week 3:
  Day 1-2: 大数据量环境准备
  Day 3-4: 极限并发压测
  Day 5: 故障恢复测试
  Day 6: 补偿处理服务压测
  Day 7: 综合报告和优化方案
```

## 🛠 压测脚本示例

### 6.1 JMeter测试计划
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan testname="Saga Performance Test">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments">
          <elementProp name="host" elementType="Argument">
            <stringProp name="Argument.name">host</stringProp>
            <stringProp name="Argument.value">localhost</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">8080</stringProp>
          </elementProp>
        </collectionProp>
      </elementProp>
    </TestPlan>
  </hashTree>
</jmeterTestPlan>
```

### 6.2 wrk压测命令
```bash
# Saga创建接口压测
wrk -t12 -c400 -d30s -s create_saga.lua http://localhost:8080/saga/transactions

# 状态查询接口压测
wrk -t8 -c200 -d30s http://localhost:8080/saga/transactions/test-saga-001

# 补偿上报接口压测
wrk -t8 -c300 -d30s -s compensation.lua http://localhost:8080/saga/transactions/compensation

# 事务提交接口压测
wrk -t4 -c100 -d30s -s commit.lua http://localhost:8080/saga/transactions/commit

# 事务回滚接口压测
wrk -t4 -c100 -d30s -s rollback.lua http://localhost:8080/saga/transactions/rollback
```

## 📊 监控指标收集

### 7.1 应用层监控
```yaml
指标类型:
  - HTTP请求指标: QPS, 响应时间, 错误率
  - 业务指标: Saga创建数, 步骤执行数, 补偿执行数
  - 资源指标: CPU, 内存, 协程数, GC时间
  - 数据库指标: 连接数, 查询时间, 慢查询

收集工具:
  - Prometheus metrics
  - Go pprof
  - 自定义业务指标
```

### 7.2 系统层监控 (macOS)
```bash
# CPU监控 (macOS)
top -pid $(pgrep saga) -l 0

# 内存监控 (macOS)
vm_stat && memory_pressure

# 磁盘I/O监控 (macOS)
iostat -w 1

# 网络监控 (macOS)
netstat -i && netstat -an | grep LISTEN

# 系统整体监控
sudo powermetrics -n 1 -i 1000 --samplers cpu_power,gpu_power,thermal

# Activity Monitor 命令行版本
sudo fs_usage -w -f filesystem | head -20

# 数据库监控 (Docker容器内)
docker exec saga-mysql mysql -u root -p12345678a -e "SHOW PROCESSLIST;"
docker exec saga-mysql mysql -u root -p12345678a -e "SHOW ENGINE INNODB STATUS;"

# Docker容器监控
docker stats saga-mysql saga-app
```

## 📋 测试数据准备脚本

### 8.1 基础数据生成
```sql
-- 使用现有数据库
USE saga;

-- 生成测试Saga事务数据
DELIMITER $$
CREATE PROCEDURE GenerateTestSagas(IN saga_count INT)
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE saga_id VARCHAR(64);
    DECLARE step_count INT;
    DECLARE j INT;

    WHILE i <= saga_count DO
        SET saga_id = CONCAT('perf-test-saga-', LPAD(i, 10, '0'));
        SET step_count = FLOOR(1 + RAND() * 5); -- 1-5个步骤

        -- 插入Saga事务
        INSERT INTO saga_transactions (
            saga_id, name, saga_status, step_index_mode,
            cur_step_index, compensation_window_sec, created_at, updated_at
        ) VALUES (
            saga_id,
            CONCAT('性能测试事务_', i),
            CASE FLOOR(RAND() * 5)
                WHEN 0 THEN 'pending'
                WHEN 1 THEN 'running'
                WHEN 2 THEN 'completed'
                WHEN 3 THEN 'compensating'
                ELSE 'failed'
            END,
            'auto',
            step_count,
            300,
            NOW(),
            NOW()
        );

        -- 插入步骤数据
        SET j = 1;
        WHILE j <= step_count DO
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status, created_at, updated_at
            ) VALUES (
                CONCAT('step-', saga_id, '-', j),
                saga_id,
                CONCAT('Action', j),
                j,
                CONCAT('service-', j),
                '{}',
                '{}',
                CONCAT('http://service-', j, '/compensate'),
                CASE FLOOR(RAND() * 6)
                    WHEN 0 THEN 'uninitialized'
                    WHEN 1 THEN 'pending'
                    WHEN 2 THEN 'running'
                    WHEN 3 THEN 'completed'
                    WHEN 4 THEN 'failed'
                    ELSE 'delay'
                END,
                NOW(),
                NOW()
            );
            SET j = j + 1;
        END WHILE;

        SET i = i + 1;

        -- 每1000条提交一次
        IF i % 1000 = 0 THEN
            COMMIT;
        END IF;
    END WHILE;

    COMMIT;
END$$
DELIMITER ;

-- 生成不同级别的测试数据
-- Level 1: 10万条
CALL GenerateTestSagas(100000);

-- Level 2: 100万条 (需要先清理数据)
-- TRUNCATE TABLE saga_steps; TRUNCATE TABLE saga_transactions;
-- CALL GenerateTestSagas(1000000);

-- Level 3: 1000万条 (需要先清理数据)
-- TRUNCATE TABLE saga_steps; TRUNCATE TABLE saga_transactions;
-- CALL GenerateTestSagas(10000000);
```

### 8.2 数据清理脚本
```sql
-- 清理测试数据
TRUNCATE TABLE saga_steps;
TRUNCATE TABLE saga_transactions;

-- 重置自增ID
ALTER TABLE saga_steps AUTO_INCREMENT = 1;
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;

-- 删除存储过程
DROP PROCEDURE IF EXISTS GenerateTestSagas;
```

### 8.3 索引优化脚本
```sql
-- 为压测优化索引
CREATE INDEX idx_saga_status ON saga_transactions(saga_status);
CREATE INDEX idx_saga_created_at ON saga_transactions(created_at);
CREATE INDEX idx_step_saga_id ON saga_steps(saga_id);
CREATE INDEX idx_step_compensation_status ON saga_steps(compensation_status);
CREATE INDEX idx_step_created_at ON saga_steps(created_at);
CREATE INDEX idx_step_saga_compensation ON saga_steps(saga_id, compensation_status);

-- 分析表统计信息
ANALYZE TABLE saga_transactions;
ANALYZE TABLE saga_steps;
```

## 🔧 Go压测脚本

### 9.1 Saga创建压测脚本
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "sync"
    "time"
    "sort"
)

type SagaCreateRequest struct {
    Name                  string `json:"name"`
    StepIndexMode        string `json:"stepIndexMode"`
    CompensationWindowSec int    `json:"compensationWindowSec"`
}

type TestResult struct {
    Duration time.Duration
    Success  bool
    Error    string
}

func createSagaWorker(baseURL string, requests int, wg *sync.WaitGroup, results chan<- TestResult) {
    defer wg.Done()

    client := &http.Client{Timeout: 30 * time.Second}

    for i := 0; i < requests; i++ {
        req := SagaCreateRequest{
            Name:                  fmt.Sprintf("压测事务_%d_%d", time.Now().UnixNano(), i),
            StepIndexMode:        "auto",
            CompensationWindowSec: 300,
        }

        jsonData, _ := json.Marshal(req)

        start := time.Now()
        resp, err := client.Post(baseURL+"/api/saga/create", "application/json", bytes.NewBuffer(jsonData))
        duration := time.Since(start)

        result := TestResult{Duration: duration}

        if err != nil {
            result.Success = false
            result.Error = err.Error()
        } else {
            result.Success = resp.StatusCode == 200
            if resp.StatusCode != 200 {
                result.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
            }
            resp.Body.Close()
        }

        results <- result
    }
}

func calculatePercentile(durations []time.Duration, percentile float64) time.Duration {
    if len(durations) == 0 {
        return 0
    }

    sort.Slice(durations, func(i, j int) bool {
        return durations[i] < durations[j]
    })

    index := int(float64(len(durations)) * percentile / 100.0)
    if index >= len(durations) {
        index = len(durations) - 1
    }

    return durations[index]
}

func main() {
    baseURL := "http://localhost:8080"
    concurrency := 100
    totalRequests := 10000
    requestsPerWorker := totalRequests / concurrency

    fmt.Printf("开始压测: 并发数=%d, 总请求数=%d\n", concurrency, totalRequests)

    var wg sync.WaitGroup
    results := make(chan TestResult, totalRequests)

    start := time.Now()

    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go createSagaWorker(baseURL, requestsPerWorker, &wg, results)
    }

    wg.Wait()
    close(results)

    totalTime := time.Since(start)

    // 统计结果
    var durations []time.Duration
    successCount := 0
    errorCount := 0

    for result := range results {
        durations = append(durations, result.Duration)
        if result.Success {
            successCount++
        } else {
            errorCount++
            fmt.Printf("错误: %s\n", result.Error)
        }
    }

    // 计算统计指标
    fmt.Printf("\n=== 压测结果 ===\n")
    fmt.Printf("总请求数: %d\n", len(durations))
    fmt.Printf("成功请求: %d\n", successCount)
    fmt.Printf("失败请求: %d\n", errorCount)
    fmt.Printf("成功率: %.2f%%\n", float64(successCount)/float64(len(durations))*100)
    fmt.Printf("总耗时: %v\n", totalTime)
    fmt.Printf("TPS: %.2f\n", float64(len(durations))/totalTime.Seconds())
    fmt.Printf("平均响应时间: %v\n", calculatePercentile(durations, 50))
    fmt.Printf("P95响应时间: %v\n", calculatePercentile(durations, 95))
    fmt.Printf("P99响应时间: %v\n", calculatePercentile(durations, 99))

    if len(durations) > 0 {
        sort.Slice(durations, func(i, j int) bool {
            return durations[i] < durations[j]
        })
        fmt.Printf("最快响应: %v\n", durations[0])
        fmt.Printf("最慢响应: %v\n", durations[len(durations)-1])
    }
}
```

### 9.2 混合场景压测脚本
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "math/rand"
    "net/http"
    "sync"
    "time"
)

type MixedTestScenario struct {
    CreateSagaWeight    int
    ReportStepWeight    int
    QueryStatusWeight   int
    ExecuteCompWeight   int
}

func mixedScenarioWorker(baseURL string, duration time.Duration, scenario MixedTestScenario, wg *sync.WaitGroup) {
    defer wg.Done()

    client := &http.Client{Timeout: 30 * time.Second}
    endTime := time.Now().Add(duration)

    totalWeight := scenario.CreateSagaWeight + scenario.ReportStepWeight +
                  scenario.QueryStatusWeight + scenario.ExecuteCompWeight

    for time.Now().Before(endTime) {
        choice := rand.Intn(totalWeight)

        switch {
        case choice < scenario.CreateSagaWeight:
            // 创建Saga
            createSaga(client, baseURL)
        case choice < scenario.CreateSagaWeight + scenario.ReportStepWeight:
            // 上报步骤
            reportStep(client, baseURL)
        case choice < scenario.CreateSagaWeight + scenario.ReportStepWeight + scenario.QueryStatusWeight:
            // 查询状态
            queryStatus(client, baseURL)
        default:
            // 执行补偿
            executeCompensation(client, baseURL)
        }

        // 随机延迟 1-10ms
        time.Sleep(time.Duration(rand.Intn(10)+1) * time.Millisecond)
    }
}

func createSaga(client *http.Client, baseURL string) {
    req := map[string]interface{}{
        "name": fmt.Sprintf("混合压测_%d", time.Now().UnixNano()),
        "stepIndexMode": "auto",
        "compensationWindowSec": 300,
    }

    jsonData, _ := json.Marshal(req)
    resp, err := client.Post(baseURL+"/api/saga/create", "application/json", bytes.NewBuffer(jsonData))
    if err == nil {
        resp.Body.Close()
    }
}

func reportStep(client *http.Client, baseURL string) {
    req := map[string]interface{}{
        "sagaId": fmt.Sprintf("perf-test-saga-%010d", rand.Intn(100000)),
        "action": "TestAction",
        "serviceName": "test-service",
        "contextData": "{}",
        "compensationContext": "{}",
        "compensateEndpoint": "http://test-service/compensate",
    }

    jsonData, _ := json.Marshal(req)
    resp, err := client.Post(baseURL+"/api/saga/report-step", "application/json", bytes.NewBuffer(jsonData))
    if err == nil {
        resp.Body.Close()
    }
}

func queryStatus(client *http.Client, baseURL string) {
    sagaId := fmt.Sprintf("perf-test-saga-%010d", rand.Intn(100000))
    resp, err := client.Get(baseURL + "/api/saga/status/" + sagaId)
    if err == nil {
        resp.Body.Close()
    }
}

func executeCompensation(client *http.Client, baseURL string) {
    req := map[string]interface{}{
        "sagaId": fmt.Sprintf("perf-test-saga-%010d", rand.Intn(100000)),
        "stepId": fmt.Sprintf("step-test-%d", rand.Intn(1000000)),
    }

    jsonData, _ := json.Marshal(req)
    resp, err := client.Post(baseURL+"/api/saga/execute-compensation", "application/json", bytes.NewBuffer(jsonData))
    if err == nil {
        resp.Body.Close()
    }
}

func main() {
    baseURL := "http://localhost:8080"
    concurrency := 50
    testDuration := 5 * time.Minute

    scenario := MixedTestScenario{
        CreateSagaWeight:  20,  // 20%
        ReportStepWeight:  40,  // 40%
        QueryStatusWeight: 30,  // 30%
        ExecuteCompWeight: 10,  // 10%
    }

    fmt.Printf("开始混合场景压测: 并发数=%d, 持续时间=%v\n", concurrency, testDuration)

    var wg sync.WaitGroup
    start := time.Now()

    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go mixedScenarioWorker(baseURL, testDuration, scenario, &wg)
    }

    wg.Wait()

    actualDuration := time.Since(start)
    fmt.Printf("混合场景压测完成，实际耗时: %v\n", actualDuration)
}
```

## 📈 性能基准和优化建议

### 10.1 性能基准 (单机环境 - MacBook Pro M3 Max)
```yaml
Level 1 (10万级):
  期望TPS: 500+
  期望P95响应时间: <800ms
  期望错误率: <0.5%
  期望总CPU使用率: <80%
  期望总内存使用率: <85%

Level 2 (100万级):
  期望TPS: 800+
  期望P95响应时间: <1,500ms
  期望错误率: <1%
  期望总CPU使用率: <85%
  期望总内存使用率: <90%

Level 3 (1000万级):
  期望TPS: 1,000+
  期望P95响应时间: <3,000ms
  期望错误率: <2%
  期望总CPU使用率: <90%
  期望总内存使用率: <95%

注意:
- 以上指标考虑了单机环境下应用、数据库、压测客户端共享资源的情况
- M3 Max芯片的高性能核心和效率核心混合架构可能影响性能表现
- 建议在实际测试中根据具体表现调整期望值
```

### 10.2 常见优化点
```yaml
数据库优化:
  - 索引优化: 为常用查询字段添加合适索引
  - 连接池调优: 调整最大连接数和空闲连接数
  - 查询优化: 优化慢查询，避免全表扫描
  - 分库分表: 大数据量时考虑水平分片
  - 读写分离: 查询操作使用只读副本

应用优化:
  - 连接池配置: HTTP客户端连接池优化
  - 缓存策略: 引入Redis缓存热点数据
  - 异步处理: 非关键路径异步化
  - 批量操作: 批量插入和更新
  - 协程池: 限制协程数量避免资源耗尽

系统优化:
  - Go运行时参数: GOMAXPROCS, GOGC调优
  - 操作系统参数: 文件描述符限制, TCP参数
  - 网络配置: 带宽和延迟优化
  - 磁盘I/O优化: SSD使用, I/O调度器优化
```

### 10.3 监控告警阈值
```yaml
应用指标:
  - TPS下降超过20%: 告警
  - P95响应时间超过阈值: 告警
  - 错误率超过1%: 告警
  - 活跃协程数超过10000: 告警

系统指标:
  - CPU使用率超过85%: 告警
  - 内存使用率超过90%: 告警
  - 磁盘使用率超过80%: 告警
  - 网络带宽使用率超过80%: 告警

数据库指标:
  - 连接数超过最大值80%: 告警
  - 慢查询数量异常: 告警
  - 锁等待时间过长: 告警
  - 主从延迟超过1秒: 告警
```

## 📊 测试报告模板

### 11.1 执行摘要
```markdown
## 性能测试执行摘要

### 测试概述
- 测试时间: 2024-XX-XX 至 2024-XX-XX
- 测试环境: [环境描述]
- 测试数据量: [10万/100万/1000万]级
- 测试工具: JMeter, wrk, 自定义Go脚本

### 关键发现
1. 系统在[数据量]级别下表现[良好/一般/较差]
2. 主要瓶颈在[数据库/应用/网络]层面
3. [具体性能问题描述]

### 结论
- 是否达到预期性能目标: [是/否]
- 系统稳定性评估: [优秀/良好/一般/较差]
- 推荐生产环境配置: [配置建议]
```

### 11.2 详细测试结果
```markdown
## 详细测试结果

### 接口性能数据
| 接口名称 | 并发数 | TPS | P50响应时间 | P95响应时间 | P99响应时间 | 错误率 |
|---------|--------|-----|------------|------------|------------|--------|
| 创建Saga | 1000 | 1500 | 50ms | 200ms | 500ms | 0.1% |
| 上报步骤 | 2000 | 2800 | 30ms | 150ms | 300ms | 0.2% |
| 查询状态 | 5000 | 8000 | 10ms | 50ms | 100ms | 0.05% |
| 执行补偿 | 500 | 800 | 100ms | 400ms | 800ms | 0.3% |

### 资源使用情况
- CPU使用率: 平均65%, 峰值82%
- 内存使用率: 平均70%, 峰值85%
- 磁盘I/O: 平均50MB/s, 峰值120MB/s
- 网络I/O: 平均30MB/s, 峰值80MB/s

### 数据库性能
- 连接数: 平均150, 峰值300
- 查询响应时间: 平均5ms, P95为20ms
- 慢查询数量: 每小时<10个
- 锁等待: 平均<1ms
```

### 11.3 优化建议
```markdown
## 优化建议

### 短期优化方案 (1-2周)
1. **数据库索引优化**
   - 为saga_id字段添加索引
   - 优化compensation_status查询索引
   - 预计性能提升: 20-30%

2. **连接池调优**
   - 增加数据库连接池大小至200
   - 调整HTTP客户端连接池
   - 预计性能提升: 10-15%

### 长期架构改进 (1-3个月)
1. **引入缓存层**
   - Redis缓存热点查询数据
   - 缓存Saga状态信息
   - 预计性能提升: 40-50%

2. **异步处理优化**
   - 补偿执行异步化
   - 状态更新批量处理
   - 预计性能提升: 25-35%

3. **分库分表方案**
   - 按saga_id哈希分片
   - 历史数据归档
   - 支持更大数据量

### 监控告警建议
1. 建立完善的性能监控体系
2. 设置合理的告警阈值
3. 定期进行性能回归测试
4. 建立性能基线和趋势分析

### 容量规划建议
1. 当前配置支持日处理量: [具体数值]
2. 建议扩容时机: [具体指标]
3. 扩容方案: [水平/垂直扩容建议]
```

## 🎯 总结

这个性能压测计划涵盖了从基础到极限的三个数据量级，通过系统性的测试方法和详细的监控指标，能够全面评估 Saga 分布式事务系统的性能表现。

### 关键要点
1. **分级测试**: 10万、100万、1000万三个数据量级
2. **全面覆盖**: 核心接口、混合场景、长时间稳定性测试
3. **详细监控**: 应用、系统、数据库多层面监控
4. **实用工具**: 提供完整的压测脚本和数据准备脚本
5. **优化指导**: 明确的性能基准和优化建议

通过执行这个压测计划，可以为 Saga 系统的性能优化和生产环境部署提供可靠的数据支撑。
