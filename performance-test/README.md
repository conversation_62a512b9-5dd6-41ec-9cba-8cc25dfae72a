# Saga 分布式事务系统 - 百万级性能测试工具

专为百万级数据量设计的 Saga 分布式事务系统性能测试工具集。

## 📁 核心文件

- **pool-based-test.lua** ⭐ - 基于真实sagaId的百万级测试脚本
- **optimized-realistic-test.lua** ⭐ - 动态sagaId管理百万级测试脚本  
- **create-saga-pool.sh** ⭐ - Saga事务池创建工具
- **quick-test-insert.sh** ⭐ - 快速数据修复工具

## 🚀 使用方法

### 百万级性能测试流程

```bash
# 1. 确保使用性能测试配置
make up-perf

# 2. 创建 Saga 事务池
./performance-test/create-saga-pool.sh 200

# 3. 修复步骤数据（如果需要）
./performance-test/quick-test-insert.sh

# 4. 执行百万级性能测试
export SAGA_POOL_FILE="performance-test/results/saga_pool_*.txt"
wrk -t8 -c50 -d60s --latency -s performance-test/pool-based-test.lua http://localhost:8080
```

## 🎯 性能基准

- **峰值QPS**: 46,506 请求/秒
- **平均延迟**: 1.03ms  
- **99% 延迟**: 2.09ms
- **错误率**: 0%
- **数据规模**: 支持478万+ Saga事务

---

**工具版本**: v3.0 - 百万级优化版
