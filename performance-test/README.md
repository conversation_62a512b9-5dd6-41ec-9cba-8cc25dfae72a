# Saga 分布式事务系统性能测试工具

本目录包含了 Saga 分布式事务系统的完整性能测试工具集，用于评估系统性能、进行优化对比和生成测试报告。

## 📁 目录结构

```
performance-test/
├── README.md                    # 本文件
├── run-tests.sh                # 自动化测试执行脚本
├── compare-results.py          # 性能对比分析工具
├── create-saga.lua             # Auto模式 Saga 事务创建测试脚本
├── manual-mode-saga.lua        # Manual模式 Saga 事务创建测试脚本
├── get-saga-transaction.lua    # 事务查询接口测试脚本
├── report-compensation.lua     # 补偿上报测试脚本
├── rollback-saga.lua           # 事务回滚测试脚本
├── mixed-scenario.lua          # 混合场景测试脚本
├── ecommerce-workflow.lua      # 电商完整工作流测试脚本
├── create-saga-pool.sh         # Saga事务池创建脚本 (优化版)
├── pool-based-test.lua         # 基于真实sagaId的测试脚本 (优化版)
├── optimized-realistic-test.lua # 动态sagaId管理测试脚本 (优化版)
├── sequential-workflow.lua     # 顺序业务流程测试脚本 (优化版)
└── results/                    # 测试结果目录
    ├── performance-test-report-20250730.md           # 基础测试报告
    ├── comprehensive-api-test-report-20250730.md     # 全接口测试报告
    ├── optimized-realistic-test-report-20250731.md   # 优化测试报告 (最新)
    ├── performance-data-20250730.json                # 基础测试数据
    ├── comprehensive-api-data-20250730.json          # 全接口测试数据
    ├── optimized-realistic-data-20250731.json        # 优化测试数据 (最新)
    ├── saga_pool_*.txt                               # Saga事务池文件
    └── comparison-report-*.md                        # 对比分析报告
```

## 🚀 快速开始

### 前置条件

1. **确保 Saga 系统运行**
   ```bash
   # 检查服务状态
   curl http://localhost:8080/hello
   
   # 检查容器状态
   docker ps | grep saga
   ```

2. **安装测试工具**
   ```bash
   # macOS
   brew install wrk
   
   # Ubuntu/Debian
   sudo apt-get install wrk
   
   # CentOS/RHEL
   sudo yum install wrk
   ```

3. **Python 环境** (用于结果对比)
   ```bash
   python3 --version  # 需要 Python 3.6+
   ```

### 执行完整性能测试

```bash
# 进入项目根目录
cd /path/to/saga

# 执行完整测试套件
./performance-test/run-tests.sh
```

### 执行单项测试

```bash
# 基础性能测试
wrk -t4 -c10 -d30s --latency -s performance-test/create-saga.lua http://localhost:8080

# 高并发测试
wrk -t12 -c100 -d60s --latency -s performance-test/create-saga.lua http://localhost:8080

# 混合场景测试
wrk -t8 -c50 -d60s --latency -s performance-test/mixed-scenario.lua http://localhost:8080
```

### 执行优化的真实业务测试 (推荐)

```bash
# 1. 创建 Saga 事务池
./performance-test/create-saga-pool.sh 200

# 2. 基于真实 sagaId 的高性能测试
export SAGA_POOL_FILE="performance-test/results/saga_pool_20250731_115146.txt"
wrk -t8 -c50 -d60s --latency -s performance-test/pool-based-test.lua http://localhost:8080

# 3. 动态 sagaId 管理测试
wrk -t4 -c20 -d60s --latency -s performance-test/optimized-realistic-test.lua http://localhost:8080

# 4. 顺序业务流程测试
wrk -t4 -c20 -d60s --latency -s performance-test/sequential-workflow.lua http://localhost:8080
```

## 📊 测试脚本说明

### 1. create-saga.lua
**用途**: 测试 Auto 模式 Saga 事务创建接口的性能
**特点**:
- 自动生成唯一的事务名称
- 使用 stepIndexMode: "auto"
- 支持多线程并发测试
- 提供详细的延迟分布统计

### 2. manual-mode-saga.lua
**用途**: 测试 Manual 模式 Saga 事务创建接口的性能
**特点**:
- 使用 stepIndexMode: "manual"
- 包含预定义的步骤模板
- 模拟复杂业务场景的事务创建
- 测试步骤模板处理性能

### 3. get-saga-transaction.lua
**用途**: 测试事务查询接口的性能
**特点**:
- 测试 GET /saga/transactions/{sagaId} 接口
- 循环使用测试 Saga ID
- 评估查询操作的响应性能

### 4. report-compensation.lua
**用途**: 测试补偿上报接口的性能
**特点**:
- 模拟真实的补偿上报场景
- 循环使用预定义的 Saga ID
- 包含完整的补偿上下文数据

### 5. rollback-saga.lua
**用途**: 测试事务回滚接口的性能
**特点**:
- 测试 POST /saga/transactions/rollback 接口
- 支持 none/sync/async 三种执行模式
- 模拟不同类型的回滚场景

### 6. ecommerce-workflow.lua
**用途**: 模拟完整的电商订单处理工作流
**特点**:
- 包含创建事务、补偿上报、提交事务的完整流程
- 模拟订单服务、库存服务、支付服务
- 最接近真实生产环境的业务场景

### 7. mixed-scenario.lua
**用途**: 模拟真实业务场景的混合操作
**特点**:
- 按比例执行创建、上报、提交操作
- 更接近生产环境的使用模式
- 综合评估系统整体性能

### 8. create-saga-pool.sh (优化版)
**用途**: 预创建真实的 Saga 事务池
**特点**:
- 批量创建真实的 saga 事务
- 提取并保存真实的 sagaId
- 100% 创建成功率
- 支持自定义池大小

### 9. pool-based-test.lua (优化版)
**用途**: 基于真实 sagaId 的性能测试
**特点**:
- 100% 使用真实的 sagaId
- 完整的业务流程模拟
- 零错误率测试
- 真实反映生产环境性能

### 10. optimized-realistic-test.lua (优化版)
**用途**: 动态 sagaId 管理的性能测试
**特点**:
- 动态提取和管理 sagaId
- 智能池管理策略
- 实时创建和分配
- 自适应负载调整

### 11. sequential-workflow.lua (优化版)
**用途**: 顺序业务流程的性能测试
**特点**:
- 按顺序执行完整业务周期
- 周期性流程管理
- 步骤跟踪和统计
- 流程完整性保证

### 12. run-tests.sh
**用途**: 自动化执行完整的测试套件
**包含测试**:
- 健康检查性能测试
- 基础功能性能测试
- 不同并发级别的性能测试
- 所有接口的专项测试
- 混合场景测试
- 极限压力测试

## 📈 结果分析

### 查看测试结果

```bash
# 查看最新的测试报告
cat performance-test/results/performance-test-report-*.md

# 查看结构化数据
cat performance-test/results/performance-data-*.json
```

### 性能对比分析

```bash
# 对比两次测试结果
python3 performance-test/compare-results.py \
    performance-data-20250730.json \
    performance-data-20250801.json
```

### 关键指标解读

| 指标 | 说明 | 优秀标准 |
|------|------|----------|
| QPS | 每秒请求数 | > 5,000 |
| 平均延迟 | 请求平均响应时间 | < 20ms |
| P99 延迟 | 99% 请求的响应时间 | < 100ms |
| 错误率 | 请求失败比例 | < 0.1% |

## 🔧 自定义测试

### 修改测试参数

```bash
# 自定义并发数和持续时间
wrk -t8 -c50 -d120s --latency -s performance-test/create-saga.lua http://localhost:8080
#    ↑   ↑    ↑
#  线程数 并发数 持续时间
```

### 创建新的测试脚本

参考现有的 `.lua` 脚本，创建针对特定场景的测试：

```lua
-- 自定义测试脚本模板
function request()
    -- 构建请求体
    local body = '{"key": "value"}'
    
    -- 设置请求方法和头部
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    
    return wrk.format(nil, "/your/endpoint")
end
```

## 📋 测试最佳实践

### 1. 测试前准备
- ✅ 确保系统处于稳定状态
- ✅ 清理测试数据（如需要）
- ✅ 记录当前系统配置
- ✅ 监控系统资源使用

### 2. 测试执行
- ✅ 从低并发开始逐步增加
- ✅ 每次测试间隔适当休息时间
- ✅ 监控系统日志和错误
- ✅ 记录测试环境变化

### 3. 结果分析
- ✅ 关注趋势而非单次结果
- ✅ 结合资源使用情况分析
- ✅ 对比历史测试数据
- ✅ 识别性能瓶颈点

## 🎯 性能基准

基于 2025-07-31 的优化测试结果，以下是 Saga 系统的最新性能基准：

### 🚀 优化后的性能基准 (基于真实 sagaId)
| 测试类型 | QPS | 平均延迟 | P99延迟 | 状态 | 提升幅度 |
|---------|-----|----------|---------|------|----------|
| **优化中等并发** | **37,599** | **0.53ms** | **1.24ms** | **🔥 突破性** | **+1,001%** |
| **优化高并发** | **46,506** | **1.03ms** | **2.09ms** | **🚀 峰值** | **+1,262%** |

### 基础性能基准 (历史参考)
| 接口类型 | QPS | 平均延迟 | P99延迟 | 状态 |
|---------|-----|----------|---------|------|
| 健康检查 | 20,231 | 0.44ms | 1.62ms | 🏆 卓越 |
| 事务查询 | 6,415 | 6.35ms | 44.44ms | 🚀 优秀 |
| 事务回滚 | 6,301 | 5.86ms | 46.58ms | ⚡ 优秀 |
| 补偿上报 | 6,089 | 5.35ms | 43.67ms | 📊 优秀 |
| Auto模式创建 | 3,414 | 2.36ms | 6.30ms | ✅ 良好 |
| Manual模式创建 | 3,105 | 6.65ms | 18.74ms | 🔧 良好 |
| 电商完整工作流 | 8,428 | 3.85ms | 14.44ms | 🔥 惊艳 |

### 并发性能基准 (历史参考)
| 场景 | QPS | 平均延迟 | P99延迟 | 状态 |
|------|-----|----------|---------|------|
| 基础性能 | 3,414 | 2.36ms | 6.30ms | ✅ 优秀 |
| 中等并发 (50) | 5,571 | 9.04ms | 28.33ms | ✅ 优秀 |
| 高并发 (100) | 11,262 | 8.85ms | 26.50ms | ✅ 卓越 |
| 极限压力 (200) | 15,334 | 13.61ms | 49.13ms | 🔥 惊艳 |

## 🚨 故障排除

### 常见问题

1. **连接被拒绝**
   ```bash
   # 检查服务状态
   curl http://localhost:8080/hello
   docker ps | grep saga
   ```

2. **测试结果异常**
   ```bash
   # 检查系统资源
   docker stats --no-stream
   
   # 查看应用日志
   docker logs saga-app --tail 50
   ```

3. **wrk 命令不存在**
   ```bash
   # 安装 wrk
   brew install wrk  # macOS
   ```

### 性能调优建议

1. **数据库优化**
   - 调整连接池大小
   - 优化 MySQL 配置参数
   - 考虑读写分离

2. **应用优化**  
   - 调整 Go 运行时参数
   - 优化内存分配策略
   - 减少不必要的日志输出

3. **系统优化**
   - 调整容器资源限制
   - 优化网络配置
   - 监控磁盘 I/O

## 📞 支持

如有问题或建议，请：
1. 查看测试日志和错误信息
2. 检查系统资源使用情况  
3. 参考历史测试数据对比
4. 联系开发团队获取支持

---

**最后更新**: 2025-07-30  
**维护者**: AI Assistant  
**版本**: v1.0
