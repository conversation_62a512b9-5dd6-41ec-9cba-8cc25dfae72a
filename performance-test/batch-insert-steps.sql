-- 批量插入步骤数据 - 简化版本
-- 为前10万个saga生成步骤数据

USE saga;

-- 创建临时表存储saga_id
CREATE TEMPORARY TABLE temp_sagas AS 
SELECT saga_id, ROW_NUMBER() OVER (ORDER BY created_at) as rn
FROM saga_transactions 
WHERE saga_id NOT IN (SELECT DISTINCT saga_id FROM saga_steps)
LIMIT 100000;

-- 为前10万个saga插入第一个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', saga_id, '-1'),
    saga_id,
    'CreateOrder',
    1,
    'order-service',
    CONCAT('{"orderId": "ORDER-', rn, '", "amount": ', (100 + (rn % 900)), ', "userId": "user-', (rn % 10000), '"}'),
    CONCAT('{"orderId": "ORDER-', rn, '", "reason": "saga_rollback"}'),
    'http://order-service:8080/compensate',
    CASE (rn % 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END
FROM temp_sagas;

-- 为前10万个saga插入第二个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', saga_id, '-2'),
    saga_id,
    'ProcessPayment',
    2,
    'payment-service',
    CONCAT('{"orderId": "ORDER-', rn, '", "paymentId": "PAY-', (rn + 100000), '", "amount": ', (100 + (rn % 900)), '}'),
    CONCAT('{"orderId": "ORDER-', rn, '", "reason": "saga_rollback"}'),
    'http://payment-service:8080/compensate',
    CASE (rn % 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END
FROM temp_sagas;

-- 为前10万个saga插入第三个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', saga_id, '-3'),
    saga_id,
    'ReserveInventory',
    3,
    'inventory-service',
    CONCAT('{"orderId": "ORDER-', rn, '", "productId": "PROD-', (rn % 1000), '", "quantity": ', (1 + (rn % 10)), '}'),
    CONCAT('{"orderId": "ORDER-', rn, '", "reason": "saga_rollback"}'),
    'http://inventory-service:8080/compensate',
    CASE (rn % 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END
FROM temp_sagas;

-- 删除临时表
DROP TEMPORARY TABLE temp_sagas;

-- 显示结果
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;
