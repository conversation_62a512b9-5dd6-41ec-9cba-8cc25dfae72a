-- 组合Saga性能测试脚本
-- 功能: 先创建事务，然后使用创建的sagaId进行补偿上报测试
-- 业务约束: 严格遵循API规范和业务逻辑

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 全局变量
local counter = 0
local created_saga_ids = {}
local saga_file = nil
local test_phase = "create" -- "create" 或 "compensation"
local phase_switch_time = 0
local compensation_counter = 0

-- 初始化函数
function init(args)
    counter = 0
    compensation_counter = 0
    test_phase = "create"
    
    -- 创建结果目录
    os.execute("mkdir -p performance-test/results")
    
    -- 打开文件用于保存创建的sagaId
    saga_file = io.open("performance-test/results/combined_created_saga_ids.txt", "w")
    if saga_file then
        print("=== 组合测试开始 ===")
        print("阶段1: 创建Saga事务 (前50%时间)")
        print("阶段2: 补偿上报测试 (后50%时间)")
        print("开始记录创建的sagaId...")
    end
    
    -- 设置阶段切换时间 (测试总时间的50%)
    phase_switch_time = os.time() + 15 -- 假设总测试时间30秒，15秒后切换
end

-- 生成请求
function request()
    local current_time = os.time()
    
    -- 检查是否需要切换到补偿阶段
    if test_phase == "create" and current_time >= phase_switch_time then
        test_phase = "compensation"
        if saga_file then
            saga_file:close()
        end
        print("\n=== 切换到补偿上报阶段 ===")
        print(string.format("已创建 %d 个saga事务", #created_saga_ids))
    end
    
    if test_phase == "create" then
        -- 创建阶段
        counter = counter + 1
        local name = "组合测试事务-" .. counter .. "-" .. math.random(100000)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "auto",
            "compensationWindowSec": 300
        }]], name)
        
        return wrk.format("POST", "/saga/transactions", nil, body)
        
    else
        -- 补偿上报阶段
        if #created_saga_ids == 0 then
            -- 如果没有创建的saga，返回健康检查
            return wrk.format("GET", "/health", nil, nil)
        end
        
        compensation_counter = compensation_counter + 1
        local saga_id = created_saga_ids[(compensation_counter % #created_saga_ids) + 1]
        
        -- 生成唯一的action和service组合
        local action = "CombinedTestAction" .. compensation_counter
        local service = "combined-test-service-" .. (compensation_counter % 50)
        
        local body = string.format([[{
            "sagaId": "%s",
            "action": "%s",
            "serviceName": "%s",
            "contextData": "{\"orderId\":\"ORDER-%d\",\"amount\":%.2f,\"testType\":\"combined\"}",
            "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"combined_test\"}",
            "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
        }]], 
            saga_id, 
            action, 
            service,
            compensation_counter,
            99.99 + (compensation_counter % 900),
            saga_id,
            action,
            service,
            action
        )
        
        return wrk.format("POST", "/saga/transactions/compensation", nil, body)
    end
end

-- 响应处理
function response(status, headers, body)
    if test_phase == "create" then
        -- 创建阶段的响应处理
        if status == 200 then
            -- 解析响应获取sagaId
            local saga_id = body:match('"sagaId":"([^"]+)"')
            if saga_id then
                if saga_file then
                    saga_file:write(saga_id .. "\n")
                    saga_file:flush()
                end
                table.insert(created_saga_ids, saga_id)
            end
        else
            print("Create Error: " .. status .. " - " .. body)
        end
    else
        -- 补偿阶段的响应处理
        if status ~= 200 then
            print("Compensation Error: " .. status .. " - " .. body)
        end
    end
end

-- 测试完成处理
function done(summary, latency, requests)
    if saga_file then
        saga_file:close()
    end
    
    print("\n=== 组合Saga性能测试结果 ===")
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    
    print("\n=== 测试阶段统计 ===")
    print(string.format("创建的saga事务数量: %d", #created_saga_ids))
    print(string.format("补偿上报请求数量: %d", compensation_counter))
    print("创建的sagaId已保存到: performance-test/results/combined_created_saga_ids.txt")
    
    print("\n=== 业务约束验证 ===")
    print("✅ 使用真实创建的sagaId进行补偿上报")
    print("✅ 确保action+service组合唯一性")
    print("✅ 符合API规范和业务逻辑")
end
