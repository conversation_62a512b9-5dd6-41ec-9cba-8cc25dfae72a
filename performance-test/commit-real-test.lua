-- 事务提交性能测试脚本 (使用真实sagaId)
-- 业务约束: 只能提交running状态的saga事务

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取running状态的sagaId (只有running状态的saga可以提交)
local saga_ids = {}
local file = io.open("performance-test/results/running_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

print("加载了 " .. #saga_ids .. " 个running状态的sagaId用于提交测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 事务提交请求体 (符合API规范)
    local body = string.format([[{
        "sagaId": "%s"
    }]], saga_id)

    return wrk.format("POST", "/saga/transactions/commit", nil, body)
end

function done(summary, latency, requests)
    print("\n=== 事务提交接口性能测试结果 ===")
    print(string.format("使用running状态sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
end
