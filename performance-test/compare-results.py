#!/usr/bin/env python3
"""
Saga 分布式事务系统性能测试结果对比工具
用于对比不同版本或优化前后的性能差异
"""

import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

class PerformanceComparator:
    def __init__(self):
        self.results_dir = "performance-test/results"
        
    def load_test_data(self, filename: str) -> Dict[str, Any]:
        """加载测试数据文件"""
        filepath = os.path.join(self.results_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"错误: 找不到文件 {filepath}")
            return {}
        except json.JSONDecodeError:
            print(f"错误: 文件 {filepath} 不是有效的 JSON 格式")
            return {}
    
    def compare_metrics(self, baseline: Dict, current: Dict) -> Dict[str, Any]:
        """对比两次测试的关键指标"""
        comparison = {}
        
        # 对比各个测试场景
        scenarios = ['basicPerformance', 'mediumConcurrency', 'highConcurrency', 'stressTest']
        
        for scenario in scenarios:
            if scenario in baseline.get('testResults', {}) and scenario in current.get('testResults', {}):
                base_data = baseline['testResults'][scenario]
                curr_data = current['testResults'][scenario]
                
                comparison[scenario] = {
                    'qps': {
                        'baseline': base_data.get('qps', 0),
                        'current': curr_data.get('qps', 0),
                        'change': self.calculate_change(base_data.get('qps', 0), curr_data.get('qps', 0)),
                        'improvement': curr_data.get('qps', 0) > base_data.get('qps', 0)
                    },
                    'avgLatency': {
                        'baseline': base_data.get('avgLatency', 0),
                        'current': curr_data.get('avgLatency', 0),
                        'change': self.calculate_change(base_data.get('avgLatency', 0), curr_data.get('avgLatency', 0)),
                        'improvement': curr_data.get('avgLatency', 0) < base_data.get('avgLatency', 0)
                    },
                    'p99Latency': {
                        'baseline': base_data.get('p99Latency', 0),
                        'current': curr_data.get('p99Latency', 0),
                        'change': self.calculate_change(base_data.get('p99Latency', 0), curr_data.get('p99Latency', 0)),
                        'improvement': curr_data.get('p99Latency', 0) < base_data.get('p99Latency', 0)
                    },
                    'errorRate': {
                        'baseline': base_data.get('errorRate', 0),
                        'current': curr_data.get('errorRate', 0),
                        'change': self.calculate_change(base_data.get('errorRate', 0), curr_data.get('errorRate', 0)),
                        'improvement': curr_data.get('errorRate', 0) <= base_data.get('errorRate', 0)
                    }
                }
        
        return comparison
    
    def calculate_change(self, baseline: float, current: float) -> float:
        """计算变化百分比"""
        if baseline == 0:
            return 0.0
        return ((current - baseline) / baseline) * 100
    
    def generate_report(self, baseline_file: str, current_file: str) -> str:
        """生成对比报告"""
        baseline_data = self.load_test_data(baseline_file)
        current_data = self.load_test_data(current_file)
        
        if not baseline_data or not current_data:
            return "无法生成报告：数据文件加载失败"
        
        comparison = self.compare_metrics(baseline_data, current_data)
        
        report = []
        report.append("# Saga 系统性能测试对比报告")
        report.append("")
        report.append(f"**基准版本**: {baseline_data.get('testInfo', {}).get('date', 'Unknown')}")
        report.append(f"**当前版本**: {current_data.get('testInfo', {}).get('date', 'Unknown')}")
        report.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 关键指标对比
        report.append("## 🎯 关键指标对比")
        report.append("")
        report.append("| 测试场景 | 指标 | 基准值 | 当前值 | 变化 | 状态 |")
        report.append("|---------|------|--------|--------|------|------|")
        
        for scenario, data in comparison.items():
            scenario_name = self.get_scenario_name(scenario)
            
            # QPS 对比
            qps_data = data['qps']
            qps_status = "✅ 提升" if qps_data['improvement'] else "⚠️ 下降"
            report.append(f"| {scenario_name} | QPS | {qps_data['baseline']:.2f} | {qps_data['current']:.2f} | {qps_data['change']:+.2f}% | {qps_status} |")
            
            # 平均延迟对比
            lat_data = data['avgLatency']
            lat_status = "✅ 改善" if lat_data['improvement'] else "⚠️ 恶化"
            report.append(f"| {scenario_name} | 平均延迟(ms) | {lat_data['baseline']:.2f} | {lat_data['current']:.2f} | {lat_data['change']:+.2f}% | {lat_status} |")
            
            # P99 延迟对比
            p99_data = data['p99Latency']
            p99_status = "✅ 改善" if p99_data['improvement'] else "⚠️ 恶化"
            report.append(f"| {scenario_name} | P99延迟(ms) | {p99_data['baseline']:.2f} | {p99_data['current']:.2f} | {p99_data['change']:+.2f}% | {p99_status} |")
        
        # 总体评估
        report.append("")
        report.append("## 📊 总体评估")
        report.append("")
        
        # 计算总体改善情况
        improvements = 0
        total_metrics = 0
        
        for scenario_data in comparison.values():
            for metric_data in scenario_data.values():
                total_metrics += 1
                if metric_data['improvement']:
                    improvements += 1
        
        improvement_rate = (improvements / total_metrics) * 100 if total_metrics > 0 else 0
        
        if improvement_rate >= 80:
            overall_status = "🎉 显著提升"
        elif improvement_rate >= 60:
            overall_status = "✅ 明显改善"
        elif improvement_rate >= 40:
            overall_status = "📈 有所改善"
        else:
            overall_status = "⚠️ 需要关注"
        
        report.append(f"**总体状态**: {overall_status}")
        report.append(f"**改善指标比例**: {improvement_rate:.1f}% ({improvements}/{total_metrics})")
        report.append("")
        
        # 建议
        report.append("## 💡 优化建议")
        report.append("")
        
        # 根据对比结果生成建议
        suggestions = self.generate_suggestions(comparison)
        for suggestion in suggestions:
            report.append(f"- {suggestion}")
        
        return "\n".join(report)
    
    def get_scenario_name(self, scenario: str) -> str:
        """获取场景中文名称"""
        names = {
            'basicPerformance': '基础性能',
            'mediumConcurrency': '中等并发',
            'highConcurrency': '高并发',
            'stressTest': '压力测试'
        }
        return names.get(scenario, scenario)
    
    def generate_suggestions(self, comparison: Dict) -> List[str]:
        """根据对比结果生成优化建议"""
        suggestions = []
        
        # 检查 QPS 下降的场景
        qps_declined = []
        latency_increased = []
        
        for scenario, data in comparison.items():
            if not data['qps']['improvement']:
                qps_declined.append(self.get_scenario_name(scenario))
            
            if not data['avgLatency']['improvement']:
                latency_increased.append(self.get_scenario_name(scenario))
        
        if qps_declined:
            suggestions.append(f"QPS 下降场景 ({', '.join(qps_declined)}): 检查代码逻辑、数据库查询优化")
        
        if latency_increased:
            suggestions.append(f"延迟增加场景 ({', '.join(latency_increased)}): 检查网络配置、缓存策略")
        
        if not qps_declined and not latency_increased:
            suggestions.append("所有指标均有改善，继续保持当前优化方向")
        
        return suggestions

    def list_available_files(self) -> List[str]:
        """列出可用的测试数据文件"""
        files = []
        if os.path.exists(self.results_dir):
            for filename in os.listdir(self.results_dir):
                if filename.startswith('performance-data-') and filename.endswith('.json'):
                    files.append(filename)
        return sorted(files)

def main():
    comparator = PerformanceComparator()

    if len(sys.argv) == 1:
        # 显示可用文件列表
        files = comparator.list_available_files()
        if not files:
            print("未找到性能测试数据文件")
            print("请先运行性能测试: ./performance-test/run-tests.sh")
            sys.exit(1)

        print("可用的性能测试数据文件:")
        for i, filename in enumerate(files, 1):
            print(f"  {i}. {filename}")
        print("\n使用方法: python compare-results.py <baseline_file> <current_file>")
        print("示例: python compare-results.py performance-data-20250730.json performance-data-20250801.json")
        sys.exit(0)

    if len(sys.argv) != 3:
        print("使用方法: python compare-results.py <baseline_file> <current_file>")
        print("示例: python compare-results.py performance-data-20250730.json performance-data-20250801.json")
        sys.exit(1)

    baseline_file = sys.argv[1]
    current_file = sys.argv[2]

    report = comparator.generate_report(baseline_file, current_file)

    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"performance-test/results/comparison-report-{timestamp}.md"

    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(report)

    print(f"对比报告已生成: {report_filename}")
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
