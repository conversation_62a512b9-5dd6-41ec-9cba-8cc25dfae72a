-- 补偿上报测试脚本 (使用创建的sagaId)
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取创建的sagaId
local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

print("加载了 " .. #saga_ids .. " 个新创建的sagaId用于补偿测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 生成唯一的action和service组合
    local action = "CreatedSagaAction" .. counter
    local service = "created-saga-service-" .. (counter % 100)
    
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"amount\":%.2f,\"source\":\"created_saga\"}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"created_saga_test\"}",
        "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
    }]], 
        saga_id, 
        action, 
        service,
        counter,
        99.99 + (counter % 900),
        saga_id,
        action,
        service,
        action
    )

    return wrk.format("POST", nil, nil, body)
end

function done(summary, latency, requests)
    print("\n=== 补偿上报测试结果 (使用创建的sagaId) ===")
    print(string.format("使用创建的sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
end
