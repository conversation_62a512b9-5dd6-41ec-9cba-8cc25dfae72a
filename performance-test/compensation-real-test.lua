-- 补偿上报性能测试脚本 (使用真实sagaId)
-- 业务约束: 使用真实存在的sagaId，符合补偿上报API规范

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取真实的sagaId池
local saga_ids = {}
local file = io.open("performance-test/results/running_saga_ids.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
end

-- 如果running状态的不够，补充pending状态的
if #saga_ids < 100 then
    local pending_file = io.open("performance-test/results/pending_saga_ids.txt", "r")
    if pending_file then
        for line in pending_file:lines() do
            if line and line ~= "" then
                table.insert(saga_ids, line)
            end
        end
        pending_file:close()
    end
end

print("加载了 " .. #saga_ids .. " 个真实sagaId用于测试")

-- 业务操作和服务列表 (符合真实业务场景)
local actions = {
    "CreateOrder", "ProcessPayment", "ReserveInventory", "SendNotification", "UpdateUserPoints",
    "ValidateAccount", "UpdateBalance", "LogTransaction",
    "CheckInventory", "ReserveStock", "UpdateWarehouse",
    "CreateUser", "SendWelcomeEmail", "InitializeProfile", "GrantPermissions",
    "ExtractData", "TransformData", "LoadData"
}

local services = {
    "order-service", "payment-service", "inventory-service", "notification-service", "user-service",
    "account-service", "audit-service", "warehouse-service", "profile-service", "auth-service",
    "etl-service", "data-service"
}

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    local action = actions[(counter % #actions) + 1]
    local service = services[(counter % #services) + 1]
    
    -- 生成符合业务逻辑的补偿上报数据
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"amount\":%.2f,\"timestamp\":%d}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"business_compensation\"}",
        "compensateEndpoint": "http://%s:8080/saga/compensate/%s"
    }]], 
        saga_id, 
        action, 
        service,
        counter,
        99.99 + (counter % 900),
        os.time(),
        saga_id,
        action,
        service,
        action
    )

    return wrk.format("POST", nil, nil, body)
end

function done(summary, latency, requests)
    print("\n=== 补偿上报接口性能测试结果 ===")
    print(string.format("使用真实sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
end
