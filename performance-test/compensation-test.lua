-- 补偿上报性能测试脚本
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 预定义的sagaId列表 (使用数据库中的真实ID)
local saga_ids = {
    "rws1w001000dbq8wrigslbdpzmbppr5u",
    "rws1w001000dbq8wrj2pywxpzn1lfvzm", 
    "rws1w001000dbq8wrl2vlexpzoj5wx0k",
    "rws1w001000dbq8wrn1su9vpzpmkdag7",
    "rws1w001000dbq8wrp6ftxhpzq65ucdt"
}

local actions = {"CreateOrder", "ProcessPayment", "ReserveInventory", "SendNotification", "UpdateStatus"}
local services = {"order-service", "payment-service", "inventory-service", "notification-service", "status-service"}

-- 请求计数器
local counter = 0

function init(args)
    counter = 0
end

function request()
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    local action = actions[(counter % #actions) + 1]
    local service = services[(counter % #services) + 1]
    
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s", 
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"userId\":\"user-%d\"}",
        "compensationContext": "{\"orderId\":\"ORDER-%d\",\"reason\":\"saga_rollback\"}",
        "compensateEndpoint": "http://%s:8080/compensate"
    }]], saga_id, action, service, counter, counter % 10000, counter, service)
    
    return wrk.format("POST", nil, nil, body)
end

function response(status, headers, body)
    if status ~= 200 then
        print("Error: " .. status .. " - " .. body)
    end
end
