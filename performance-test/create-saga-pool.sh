#!/bin/bash

# 创建 Saga 事务池的脚本
# 预先创建一批 saga 事务，保存 sagaId 到文件中

set -e

BASE_URL="http://localhost:8080"
POOL_SIZE=${1:-200}  # 默认创建200个saga
RESULTS_DIR="performance-test/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SAGA_IDS_FILE="$RESULTS_DIR/saga_pool_$TIMESTAMP.txt"

# 创建结果目录
mkdir -p "$RESULTS_DIR"

echo "=========================================="
echo "创建 Saga 事务池"
echo "目标数量: $POOL_SIZE"
echo "时间: $(date)"
echo "=========================================="

# 检查服务状态
echo "检查服务状态..."
if ! curl -s "$BASE_URL/hello" > /dev/null; then
    echo "错误: Saga 服务不可用"
    exit 1
fi
echo "✅ 服务状态正常"

# 清空文件
> "$SAGA_IDS_FILE"

echo ""
echo "开始创建 Saga 事务池..."

# 批量创建 saga 事务
success_count=0
for i in $(seq 1 $POOL_SIZE); do
    if [ $((i % 50)) -eq 0 ]; then
        echo "进度: $i/$POOL_SIZE (成功: $success_count)"
    fi
    
    # 创建 saga 事务
    response=$(curl -s -X POST "$BASE_URL/saga/transactions" \
        -H "Content-Type: application/json" \
        -d "{\"name\": \"pool-saga-$i-$TIMESTAMP\", \"stepIndexMode\": \"auto\"}")
    
    # 提取 sagaId
    saga_id=$(echo "$response" | grep -o '"sagaId":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$saga_id" ]; then
        echo "$saga_id" >> "$SAGA_IDS_FILE"
        success_count=$((success_count + 1))
    else
        echo "警告: 第 $i 个事务创建失败: $response"
    fi
    
    # 避免过快请求
    sleep 0.01
done

echo ""
echo "=========================================="
echo "Saga 事务池创建完成！"
echo "目标数量: $POOL_SIZE"
echo "成功创建: $success_count"
echo "成功率: $(echo "scale=2; $success_count * 100 / $POOL_SIZE" | bc)%"
echo "文件位置: $SAGA_IDS_FILE"
echo "时间: $(date)"
echo "=========================================="

# 显示前几个 sagaId 作为示例
echo ""
echo "示例 Saga IDs:"
head -5 "$SAGA_IDS_FILE" | nl

echo ""
echo "使用方法:"
echo "export SAGA_POOL_FILE=\"$SAGA_IDS_FILE\""
echo "wrk -t4 -c20 -d60s --latency -s performance-test/pool-based-test.lua http://localhost:8080"
