-- Saga事务创建性能测试脚本
wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 请求计数器
local counter = 0

-- 初始化函数
function init(args)
    counter = 0
end

-- 生成请求
function request()
    counter = counter + 1
    local name = "性能测试事务-" .. counter .. "-" .. math.random(100000)
    local body = '{"name": "' .. name .. '", "stepIndexMode": "auto"}'
    return wrk.format("POST", nil, nil, body)
end

-- 响应处理
function response(status, headers, body)
    if status ~= 200 then
        print("Error: " .. status .. " - " .. body)
    end
end
