-- Saga 事务创建性能测试脚本
-- 用于 wrk 压力测试工具

-- 初始化计数器
local counter = 0
local thread_id = 0

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
end

-- 请求生成函数
function request()
    counter = counter + 1
    local saga_name = string.format("perf-test-saga-%d-%d", thread_id, counter)
    
    -- 构建请求体
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto"
    }]], saga_name)
    
    -- 设置请求头
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    
    return wrk.format(nil, "/saga/transactions")
end

-- 响应处理函数
function response(status, headers, body)
    if status ~= 200 then
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("Saga 事务创建性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
end
