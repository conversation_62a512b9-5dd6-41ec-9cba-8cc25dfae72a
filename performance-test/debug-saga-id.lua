-- 调试 sagaId 提取的测试脚本

local counter = 0
local thread_id = 0
local extracted_ids = {}

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
end

-- 从响应中提取 sagaId
function extract_saga_id(body)
    if not body then 
        print("Body is nil")
        return nil 
    end
    
    print("Response body: " .. body)
    
    -- 尝试多种模式
    local saga_id1 = string.match(body, '"sagaId":"([^"]+)"')
    local saga_id2 = string.match(body, '"sagaId":"([a-zA-Z0-9]+)"')
    local saga_id3 = string.match(body, 'sagaId":"([^"]*)"')
    
    print("Pattern 1 result: " .. (saga_id1 or "nil"))
    print("Pattern 2 result: " .. (saga_id2 or "nil"))
    print("Pattern 3 result: " .. (saga_id3 or "nil"))
    
    return saga_id1 or saga_id2 or saga_id3
end

-- 请求生成函数
function request()
    counter = counter + 1
    
    -- 只创建事务来测试 sagaId 提取
    local saga_name = string.format("debug-saga-%d-%d", thread_id, counter)
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto"
    }]], saga_name)
    
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    return wrk.format(nil, "/saga/transactions")
end

-- 响应处理函数
function response(status, headers, body)
    print(string.format("Status: %d", status))
    
    if status == 200 then
        local saga_id = extract_saga_id(body)
        if saga_id then
            table.insert(extracted_ids, saga_id)
            print("Successfully extracted sagaId: " .. saga_id)
        else
            print("Failed to extract sagaId")
        end
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("SagaId 提取调试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("提取的 sagaId 数量: %d\n", #extracted_ids))
    io.write("------------------------------\n")
    
    -- 显示前几个提取的 ID
    for i = 1, math.min(5, #extracted_ids) do
        io.write(string.format("SagaId %d: %s\n", i, extracted_ids[i]))
    end
    
    io.write("------------------------------\n")
end
