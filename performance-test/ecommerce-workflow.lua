-- 电商订单处理完整工作流性能测试
-- 模拟真实的电商业务场景：创建订单 -> 库存预留 -> 支付处理 -> 提交事务

local counter = 0
local thread_id = 0
local created_sagas = {}

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
end

-- 请求生成函数
function request()
    counter = counter + 1
    local request_type = counter % 5  -- 5个步骤的完整流程
    
    if request_type == 1 then
        -- 步骤1: 创建电商订单 Saga 事务
        local saga_name = string.format("ecommerce-order-%d-%d", thread_id, counter)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "manual",
            "stepTemplates": [
                {
                    "step_index": 1,
                    "service": "order-service",
                    "action": "CreateOrder",
                    "description": "创建订单"
                },
                {
                    "step_index": 2,
                    "service": "inventory-service",
                    "action": "ReserveInventory", 
                    "description": "库存预留"
                },
                {
                    "step_index": 3,
                    "service": "payment-service",
                    "action": "ProcessPayment",
                    "description": "处理支付"
                }
            ]
        }]], saga_name)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions")
        
    elseif request_type == 2 then
        -- 步骤2: 订单服务上报补偿信息
        local saga_id = string.format("ecommerce-saga-%d-%d", thread_id, math.floor(counter/5))
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "CreateOrder",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%s\",\"userId\":\"user-%d\",\"amount\":299.99}",
            "compensationContext": "{\"orderId\":\"%s\",\"reason\":\"cancel_order\"}",
            "compensateEndpoint": "http://order-service:8080/orders/compensate"
        }]], saga_id, order_id, counter, order_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif request_type == 3 then
        -- 步骤3: 库存服务上报补偿信息
        local saga_id = string.format("ecommerce-saga-%d-%d", thread_id, math.floor(counter/5))
        local sku = string.format("SKU-%d", counter % 100 + 1)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "ReserveInventory",
            "serviceName": "inventory-service",
            "contextData": "{\"sku\":\"%s\",\"quantity\":2,\"warehouseId\":\"WH-001\"}",
            "compensationContext": "{\"sku\":\"%s\",\"quantity\":2,\"reason\":\"release_inventory\"}",
            "compensateEndpoint": "http://inventory-service:8080/inventory/compensate"
        }]], saga_id, sku, sku)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif request_type == 4 then
        -- 步骤4: 支付服务上报补偿信息
        local saga_id = string.format("ecommerce-saga-%d-%d", thread_id, math.floor(counter/5))
        local payment_id = string.format("PAY-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "ProcessPayment",
            "serviceName": "payment-service",
            "contextData": "{\"paymentId\":\"%s\",\"amount\":299.99,\"currency\":\"CNY\"}",
            "compensationContext": "{\"paymentId\":\"%s\",\"refundAmount\":299.99,\"reason\":\"saga_rollback\"}",
            "compensateEndpoint": "http://payment-service:8080/payments/compensate"
        }]], saga_id, payment_id, payment_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    else
        -- 步骤5: 提交事务
        local saga_id = string.format("ecommerce-saga-%d-%d", thread_id, math.floor(counter/5))
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
    end
end

-- 响应处理函数
function response(status, headers, body)
    if status ~= 200 and status ~= 404 then
        -- 404 对于不存在的 saga ID 是正常的
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("电商订单处理工作流性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("业务场景: 电商订单处理完整流程\n")
    io.write("流程步骤: 创建事务 -> 订单补偿 -> 库存补偿 -> 支付补偿 -> 提交事务\n")
    io.write("模拟服务: order-service, inventory-service, payment-service\n")
    io.write("------------------------------\n")
end
