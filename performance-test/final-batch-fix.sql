-- 最终批量修复 - 使用简单的方法达到3:1比例
USE saga;

-- 创建一个临时表存储需要处理的saga
CREATE TEMPORARY TABLE temp_saga_list AS
SELECT saga_id, ROW_NUMBER() OVER (ORDER BY created_at) as rn
FROM saga_transactions
LIMIT 50000;

-- 批量插入CreateOrder步骤（为前50000个saga）
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('batch-create-', rn) as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    CONCAT('{"orderId": "ORDER-', LPAD(rn, 8, '0'), '", "amount": 299.99, "userId": "user-', (rn % 10000), '"}') as context_data,
    CONCAT('{"orderId": "ORDER-', LPAD(rn, 8, '0'), '", "reason": "saga_rollback"}') as compensation_context,
    'http://order-service:8080/saga/compensate' as compensate_endpoint,
    CASE (rn % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status
FROM temp_saga_list;

-- 批量插入ProcessPayment步骤（为前50000个saga）
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('batch-payment-', rn) as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    CONCAT('{"paymentId": "PAY-', LPAD(rn, 8, '0'), '", "amount": 299.99, "method": "alipay"}') as context_data,
    CONCAT('{"paymentId": "PAY-', LPAD(rn, 8, '0'), '", "reason": "saga_rollback"}') as compensation_context,
    'http://payment-service:8080/saga/compensate' as compensate_endpoint,
    CASE (rn % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status
FROM temp_saga_list;

-- 批量插入ReserveInventory步骤（为前50000个saga）
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('batch-inventory-', rn) as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    CONCAT('{"productId": "PROD-', (rn % 1000), '", "quantity": ', (rn % 10 + 1), ', "warehouseId": "WH-', (rn % 100), '"}') as context_data,
    CONCAT('{"productId": "PROD-', (rn % 1000), '", "reason": "saga_rollback"}') as compensation_context,
    'http://inventory-service:8080/saga/compensate' as compensate_endpoint,
    CASE (rn % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status
FROM temp_saga_list;

-- 清理临时表
DROP TEMPORARY TABLE temp_saga_list;

-- 显示最终统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示比例
SELECT 
    CONCAT('最终比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6),
           ':1'
    ) as final_ratio;

-- 显示步骤分布
SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

-- 显示有完整3个步骤的saga数量
SELECT 
    COUNT(*) as complete_sagas_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) as percentage_of_total
FROM (
    SELECT saga_id
    FROM saga_steps
    GROUP BY saga_id
    HAVING COUNT(DISTINCT action) = 3
) t;
