-- 最终修复步骤数据 - 考虑唯一约束
-- 为每个saga生成不同的action步骤

USE saga;

-- 删除测试步骤数据，保留原始数据
DELETE FROM saga_steps WHERE step_id LIKE 'test-step-%';

-- 批量插入步骤数据，每次插入1万条，避免内存问题
-- 第一批：CreateOrder步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', SUBSTRING(saga_id, 1, 8), '-1') as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "amount": 299.99, "userId": "user-1001"}') as context_data,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "reason": "saga_rollback"}') as compensation_context,
    'http://order-service:8080/compensate' as compensate_endpoint,
    'uninitialized' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'CreateOrder'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 第二批：ProcessPayment步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', SUBSTRING(saga_id, 1, 8), '-2') as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "paymentId": "PAY-123456", "amount": 299.99}') as context_data,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "reason": "saga_rollback"}') as compensation_context,
    'http://payment-service:8080/compensate' as compensate_endpoint,
    'pending' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ProcessPayment'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 第三批：ReserveInventory步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', SUBSTRING(saga_id, 1, 8), '-3') as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "productId": "PROD-001", "quantity": 2}') as context_data,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "reason": "saga_rollback"}') as compensation_context,
    'http://inventory-service:8080/compensate' as compensate_endpoint,
    'completed' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ReserveInventory'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 第四批：SendNotification步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('step-', SUBSTRING(saga_id, 1, 8), '-4') as step_id,
    saga_id,
    'SendNotification' as action,
    4 as step_index,
    'notification-service' as service_name,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "userId": "user-1001", "type": "order_confirmation"}') as context_data,
    CONCAT('{"orderId": "ORDER-', SUBSTRING(saga_id, -8), '", "reason": "saga_rollback"}') as compensation_context,
    'http://notification-service:8080/compensate' as compensate_endpoint,
    'failed' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'SendNotification'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 显示最终结果
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

SELECT 
    compensation_status,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;
