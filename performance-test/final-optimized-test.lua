-- 最终优化的真实业务流程测试脚本
-- 基于真实 sagaId 的完整业务流程

local counter = 0
local thread_id = 0
local created_sagas = {}
local max_saga_pool = 100

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    created_sagas = {}
end

-- 从响应中提取 sagaId（简化版本）
function extract_saga_id(body)
    if not body then return nil end
    local saga_id = string.match(body, '"sagaId":"([^"]+)"')
    return saga_id
end

-- 获取可用的 saga ID
function get_available_saga_id()
    if #created_sagas == 0 then
        return nil
    end
    local index = (counter % #created_sagas) + 1
    return created_sagas[index]
end

-- 请求生成函数
function request()
    counter = counter + 1
    local operation_type = counter % 10
    
    if operation_type <= 3 then
        -- 30% 概率创建新的 Saga 事务
        local saga_name = string.format("final-test-saga-%d-%d", thread_id, counter)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "auto"
        }]], saga_name)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions")
        
    elseif operation_type <= 5 then
        -- 20% 概率上报补偿信息
        local saga_id = get_available_saga_id()
        if not saga_id then
            -- 如果没有可用的 saga，创建一个新的
            return create_new_saga()
        end
        
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "CreateOrder",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%s\",\"amount\":299.99}",
            "compensationContext": "{\"orderId\":\"%s\",\"reason\":\"cancel_order\"}",
            "compensateEndpoint": "http://order-service:8080/orders/compensate"
        }]], saga_id, order_id, order_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif operation_type <= 7 then
        -- 20% 概率查询事务状态
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
        
    elseif operation_type <= 9 then
        -- 20% 概率提交事务
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
        
    else
        -- 10% 概率回滚事务
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        local body = string.format([[{
            "sagaId": "%s",
            "failReason": "测试回滚场景",
            "failedStep": "CreateOrder",
            "executionMode": "none"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/rollback")
    end
end

-- 创建新 saga 的辅助函数
function create_new_saga()
    local saga_name = string.format("fallback-saga-%d-%d", thread_id, counter)
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto"
    }]], saga_name)
    
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    return wrk.format(nil, "/saga/transactions")
end

-- 响应处理函数
function response(status, headers, body)
    if status == 200 then
        -- 如果是创建事务的成功响应，提取并保存 sagaId
        if wrk.method == "POST" and string.find(wrk.path, "/saga/transactions$") then
            local saga_id = extract_saga_id(body)
            if saga_id and #created_sagas < max_saga_pool then
                table.insert(created_sagas, saga_id)
            end
        end
    end
    -- 忽略 404 错误（事务不存在是正常的）
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("最终优化的真实业务流程测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("线程 %d 管理的 Saga 数量: %d\n", thread_id, #created_sagas))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("操作分布:\n")
    io.write("- 创建事务: 30%\n")
    io.write("- 补偿上报: 20%\n")
    io.write("- 状态查询: 20%\n")
    io.write("- 事务提交: 20%\n")
    io.write("- 事务回滚: 10%\n")
    io.write("特点: 基于真实 sagaId 的完整业务流程\n")
    io.write("优化: 动态管理 sagaId 池，确保操作基于真实事务\n")
    io.write("------------------------------\n")
end
