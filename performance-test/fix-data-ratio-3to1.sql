-- 修复数据比例问题 - 确保步骤表和事务表保持3:1比例
-- 根据 tables.sql 表结构，为每个saga事务创建3个步骤

USE saga;

-- 清理现有的测试步骤数据，保留原始业务数据
DELETE FROM saga_steps WHERE step_id LIKE 'step-%' OR step_id LIKE 'create-%' OR step_id LIKE 'payment-%' OR step_id LIKE 'inventory-%' OR step_id LIKE 'test-%';

-- 检查当前数据状态
SELECT 
    'Before Fix - saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'Before Fix - saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 创建临时表来分批处理数据
CREATE TEMPORARY TABLE temp_saga_batch AS 
SELECT 
    saga_id,
    name,
    created_at,
    ROW_NUMBER() OVER (ORDER BY created_at) as batch_num
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps 
    WHERE step_id NOT LIKE 'e255b7fc%'  -- 保留原始业务数据
);

-- 分批插入数据，每批处理50000个saga（避免内存问题）
-- 第一批：前50000个saga的第一个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT(SUBSTRING(MD5(CONCAT(saga_id, '1')), 1, 32)) as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'userId', CONCAT('user-', (batch_num % 10000) + 1),
        'amount', ROUND(99.99 + (batch_num % 900), 2),
        'productId', CONCAT('PROD-', (batch_num % 1000) + 1),
        'timestamp', UNIX_TIMESTAMP(created_at)
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'reason', 'saga_rollback',
        'step', 1,
        'service', 'order-service'
    ) as compensation_context,
    'http://order-service:8080/saga/compensate' as compensate_endpoint,
    CASE (batch_num % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status,
    created_at,
    created_at as updated_at
FROM temp_saga_batch
WHERE batch_num <= 50000;

-- 第一批：前50000个saga的第二个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT(SUBSTRING(MD5(CONCAT(saga_id, '2')), 1, 32)) as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'paymentId', CONCAT('PAY-', LPAD(batch_num + 100000, 8, '0')),
        'amount', ROUND(99.99 + (batch_num % 900), 2),
        'method', CASE (batch_num % 3) WHEN 0 THEN 'alipay' WHEN 1 THEN 'wechat' ELSE 'bank' END,
        'timestamp', UNIX_TIMESTAMP(created_at)
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'paymentId', CONCAT('PAY-', LPAD(batch_num + 100000, 8, '0')),
        'reason', 'saga_rollback',
        'step', 2,
        'service', 'payment-service'
    ) as compensation_context,
    'http://payment-service:8080/saga/compensate' as compensate_endpoint,
    CASE (batch_num % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status,
    created_at,
    created_at as updated_at
FROM temp_saga_batch
WHERE batch_num <= 50000;

-- 第一批：前50000个saga的第三个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT(SUBSTRING(MD5(CONCAT(saga_id, '3')), 1, 32)) as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'productId', CONCAT('PROD-', (batch_num % 1000) + 1),
        'quantity', (batch_num % 10) + 1,
        'warehouseId', CONCAT('WH-', (batch_num % 100) + 1),
        'timestamp', UNIX_TIMESTAMP(created_at)
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(batch_num, 8, '0')),
        'productId', CONCAT('PROD-', (batch_num % 1000) + 1),
        'reason', 'saga_rollback',
        'step', 3,
        'service', 'inventory-service'
    ) as compensation_context,
    'http://inventory-service:8080/saga/compensate' as compensate_endpoint,
    CASE (batch_num % 6)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'running'
        WHEN 3 THEN 'completed'
        WHEN 4 THEN 'failed'
        ELSE 'delay'
    END as compensation_status,
    created_at,
    created_at as updated_at
FROM temp_saga_batch
WHERE batch_num <= 50000;

-- 显示第一批处理结果
SELECT CONCAT('第一批处理完成 - 处理了前50000个saga，生成了', COUNT(*), '条步骤数据') as progress
FROM saga_steps 
WHERE step_id LIKE CONCAT(SUBSTRING(MD5('test'), 1, 8), '%');

-- 清理临时表
DROP TEMPORARY TABLE temp_saga_batch;

-- 显示修复后的数据统计
SELECT 
    'After Fix - saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'After Fix - saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示数据比例
SELECT 
    CONCAT('数据比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' = ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2),
           ':1'
    ) as data_ratio;

-- 显示步骤数据分布
SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

-- 显示补偿状态分布
SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;
