-- 修复百万级测试数据 - 为现有的Saga事务生成步骤数据
-- 解决步骤数据缺失的问题

USE saga;

-- 删除现有的测试步骤数据（保留真实业务数据）
DELETE FROM saga_steps WHERE step_id LIKE 'step-%';

-- 为现有的Saga事务批量生成步骤数据
DELIMITER $$
CREATE PROCEDURE GenerateStepsForExistingSagas()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE saga_id_var VARCHAR(64);
    DECLARE saga_name_var VARCHAR(255);
    DECLARE step_count INT;
    DECLARE j INT;
    DECLARE batch_count INT DEFAULT 0;
    
    -- 声明游标
    DECLARE saga_cursor CURSOR FOR 
        SELECT saga_id, name 
        FROM saga_transactions 
        WHERE saga_id LIKE 'quick-test-saga-%' OR saga_id LIKE 'perf-million-saga-%'
        ORDER BY saga_id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    OPEN saga_cursor;
    
    read_loop: LOOP
        FETCH saga_cursor INTO saga_id_var, saga_name_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 为每个saga生成2-4个步骤
        SET step_count = 2 + FLOOR(RAND() * 3);
        SET j = 1;
        
        WHILE j <= step_count DO
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status, created_at, updated_at
            ) VALUES (
                CONCAT('step-', saga_id_var, '-', j),
                saga_id_var,
                CASE j
                    WHEN 1 THEN 'CreateOrder'
                    WHEN 2 THEN 'ProcessPayment'
                    WHEN 3 THEN 'ReserveInventory'
                    ELSE 'SendNotification'
                END,
                j,
                CASE j
                    WHEN 1 THEN 'order-service'
                    WHEN 2 THEN 'payment-service'
                    WHEN 3 THEN 'inventory-service'
                    ELSE 'notification-service'
                END,
                JSON_OBJECT(
                    'orderId', CONCAT('ORDER-', SUBSTRING(saga_id_var, -6)),
                    'amount', ROUND(100 + RAND() * 900, 2),
                    'userId', CONCAT('user-', FLOOR(RAND() * 10000)),
                    'productId', CONCAT('PROD-', FLOOR(RAND() * 1000))
                ),
                JSON_OBJECT(
                    'orderId', CONCAT('ORDER-', SUBSTRING(saga_id_var, -6)),
                    'reason', 'saga_rollback',
                    'step', j,
                    'timestamp', NOW()
                ),
                CASE j
                    WHEN 1 THEN 'http://order-service:8080/compensate'
                    WHEN 2 THEN 'http://payment-service:8080/compensate'
                    WHEN 3 THEN 'http://inventory-service:8080/compensate'
                    ELSE 'http://notification-service:8080/compensate'
                END,
                CASE FLOOR(RAND() * 4)
                    WHEN 0 THEN 'uninitialized'
                    WHEN 1 THEN 'pending'
                    WHEN 2 THEN 'completed'
                    ELSE 'failed'
                END,
                DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
                NOW()
            );
            SET j = j + 1;
        END WHILE;
        
        SET batch_count = batch_count + 1;
        
        -- 每1000条提交一次
        IF batch_count % 1000 = 0 THEN
            COMMIT;
            START TRANSACTION;
            SELECT CONCAT('已处理 ', batch_count, ' 个 Saga 事务的步骤数据') AS progress;
        END IF;
        
    END LOOP;
    
    CLOSE saga_cursor;
    COMMIT;
    
    SELECT CONCAT('步骤数据生成完成！处理了 ', batch_count, ' 个 Saga 事务') AS result;
END$$
DELIMITER ;

-- 执行步骤数据生成
CALL GenerateStepsForExistingSagas();

-- 删除存储过程
DROP PROCEDURE GenerateStepsForExistingSagas;

-- 更新统计信息
ANALYZE TABLE saga_steps;

-- 显示修复后的数据统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示步骤状态分布
SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;

-- 显示服务分布
SELECT 
    service_name,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY service_name
ORDER BY count DESC;
