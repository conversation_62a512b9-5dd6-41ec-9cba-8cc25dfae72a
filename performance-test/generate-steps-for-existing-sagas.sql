-- 为现有的所有Saga事务生成步骤数据
-- 针对测试期间创建的真实saga生成对应的步骤

USE saga;

-- 批量为现有saga生成步骤数据
-- 使用INSERT...SELECT语句提高效率

-- 为每个saga生成第一个步骤（CreateOrder）
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT('step-', saga_id, '-1') as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'amount', ROUND(100 + (RAND() * 900), 2),
        'userId', CONCAT('user-', FLOOR(RAND() * 10000)),
        'productId', CONCAT('PROD-', FLOOR(RAND() * 1000))
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'reason', 'saga_rollback',
        'step', 1
    ) as compensation_context,
    'http://order-service:8080/compensate' as compensate_endpoint,
    CASE FLOOR(RAND() * 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END as compensation_status,
    created_at,
    updated_at
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE step_index = 1
)
LIMIT 100000;

-- 为每个saga生成第二个步骤（ProcessPayment）
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT('step-', saga_id, '-2') as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'paymentId', CONCAT('PAY-', FLOOR(RAND() * 1000000)),
        'amount', ROUND(100 + (RAND() * 900), 2),
        'method', 'alipay'
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'reason', 'saga_rollback',
        'step', 2
    ) as compensation_context,
    'http://payment-service:8080/compensate' as compensate_endpoint,
    CASE FLOOR(RAND() * 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END as compensation_status,
    created_at,
    updated_at
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE step_index = 2
)
LIMIT 100000;

-- 为每个saga生成第三个步骤（ReserveInventory）
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, created_at, updated_at
)
SELECT 
    CONCAT('step-', saga_id, '-3') as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'productId', CONCAT('PROD-', FLOOR(RAND() * 1000)),
        'quantity', FLOOR(1 + RAND() * 10),
        'warehouseId', CONCAT('WH-', FLOOR(RAND() * 100))
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', RIGHT(saga_id, 8)),
        'reason', 'saga_rollback',
        'step', 3
    ) as compensation_context,
    'http://inventory-service:8080/compensate' as compensate_endpoint,
    CASE FLOOR(RAND() * 4)
        WHEN 0 THEN 'uninitialized'
        WHEN 1 THEN 'pending'
        WHEN 2 THEN 'completed'
        ELSE 'failed'
    END as compensation_status,
    created_at,
    updated_at
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE step_index = 3
)
LIMIT 100000;

-- 更新统计信息
ANALYZE TABLE saga_steps;

-- 显示修复后的数据统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示步骤分布
SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

-- 显示补偿状态分布
SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;
