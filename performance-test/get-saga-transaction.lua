-- Saga 事务查询性能测试脚本
-- 测试 GET /saga/transactions/{sagaId} 接口

-- 预定义的 Saga ID 列表（需要先创建一些事务）
local saga_ids = {}
local counter = 0
local thread_id = 0

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    
    -- 预填充一些 Saga ID（实际使用时需要先创建）
    -- 这里使用一些可能存在的 ID 格式
    for i = 1, 1000 do
        table.insert(saga_ids, string.format("test-saga-%d-%d", thread_id, i))
    end
end

-- 请求生成函数
function request()
    counter = counter + 1

    -- 循环使用 Saga ID，确保数组不为空
    if #saga_ids == 0 then
        -- 如果没有预定义 ID，使用默认格式
        local saga_id = string.format("test-saga-%d-%d", thread_id, counter)
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
    end

    local saga_id_index = (counter % #saga_ids) + 1
    local saga_id = saga_ids[saga_id_index]

    -- 设置请求方法
    wrk.method = "GET"
    wrk.body = ""
    wrk.headers["Content-Type"] = "application/json"

    return wrk.format(nil, "/saga/transactions/" .. saga_id)
end

-- 响应处理函数
function response(status, headers, body)
    -- 记录不同状态码的响应
    if status == 200 then
        -- 成功找到事务
    elseif status == 404 then
        -- 事务不存在（这是正常的，因为我们使用的是测试 ID）
    else
        print("Unexpected response: " .. status .. " - " .. body)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("Saga 事务查询性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("注意: 404 响应是正常的，因为使用的是测试 ID\n")
    io.write("------------------------------\n")
end
