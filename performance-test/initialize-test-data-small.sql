-- Saga 分布式事务系统 - 初始化测试数据脚本 (小规模测试版)
-- 生成时间: 2025年7月31日
-- 目标: 创建1000个saga和3000-5000个步骤，验证脚本正确性

USE saga;

-- 清理现有测试数据
TRUNCATE TABLE saga_steps;
TRUNCATE TABLE saga_transactions;

-- 重置自增ID
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;

-- 设置变量
SET @total_sagas = 1000; -- 总saga数量 (小规模测试)

-- 生成Saga事务数据
INSERT INTO saga_transactions (
    saga_id, name, saga_status, step_index_mode, 
    cur_step_index, compensation_window_sec, created_at, updated_at
)
SELECT 
    -- 生成UUID格式的saga_id (36位标准UUID格式)
    CONCAT(
        SUBSTRING(MD5(CONCAT('saga', n, UNIX_TIMESTAMP())), 1, 8), '-',
        SUBSTRING(MD5(CONCAT('test', n, RAND())), 1, 4), '-',
        '4000', '-',
        SUBSTRING(MD5(CONCAT('data', n, NOW())), 1, 4), '-',
        SUBSTRING(MD5(CONCAT('init', n, CONNECTION_ID())), 1, 12)
    ) as saga_id,
    
    -- 生成业务相关的名称
    CASE (n % 5)
        WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n, 6, '0'))
        WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n, 6, '0'))
        WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n, 6, '0'))
        WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n, 6, '0'))
        ELSE CONCAT('数据同步流程-', LPAD(n, 6, '0'))
    END as name,
    
    -- 生成符合业务逻辑的状态分布
    CASE 
        WHEN (n % 100) < 60 THEN 'running'      -- 60% running
        WHEN (n % 100) < 80 THEN 'completed'    -- 20% completed  
        WHEN (n % 100) < 90 THEN 'pending'      -- 10% pending
        WHEN (n % 100) < 95 THEN 'compensating' -- 5% compensating
        ELSE 'failed'                           -- 5% failed
    END as saga_status,
    
    -- 步骤索引模式分布
    CASE WHEN (n % 3) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode,
    
    -- 当前步骤索引
    CASE 
        WHEN (n % 100) < 60 THEN (n % 5) + 1  -- running状态有当前步骤
        WHEN (n % 100) < 80 THEN 5            -- completed状态完成所有步骤
        ELSE 0                                -- 其他状态
    END as cur_step_index,
    
    -- 补偿窗口时间
    CASE (n % 5)
        WHEN 0 THEN 300  -- 电商5分钟
        WHEN 1 THEN 600  -- 支付10分钟
        WHEN 2 THEN 180  -- 库存3分钟
        WHEN 3 THEN 120  -- 用户2分钟
        ELSE 240         -- 其他4分钟
    END as compensation_window_sec,
    
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND) as created_at, -- 最近2小时内创建
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) as updated_at  -- 最近1小时内更新

FROM (
    SELECT a.N + b.N * 10 + c.N * 100 as n
    FROM 
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
) numbers
WHERE n < @total_sagas;

-- 显示Saga事务创建进度
SELECT CONCAT('已创建 ', COUNT(*), ' 个Saga事务') as progress FROM saga_transactions;

-- 为每个saga创建3-5个步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, retry_count, created_at, updated_at
)
SELECT 
    -- 生成唯一的step_id (32位)
    MD5(CONCAT(t.saga_id, step_info.action, step_info.step_index, t.id)) as step_id,
    
    t.saga_id,
    step_info.action,
    step_info.step_index,
    step_info.service_name,
    
    -- 生成context_data
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', LPAD(t.id, 8, '0')),
        'stepIndex', step_info.step_index,
        'action', step_info.action,
        'timestamp', UNIX_TIMESTAMP(t.created_at)
    ) as context_data,
    
    -- 生成compensation_context
    JSON_OBJECT(
        'sagaId', t.saga_id,
        'stepIndex', step_info.step_index,
        'action', step_info.action,
        'service', step_info.service_name,
        'reason', 'saga_rollback'
    ) as compensation_context,
    
    step_info.compensate_endpoint,
    
    -- 生成补偿状态
    CASE 
        WHEN t.saga_status = 'completed' THEN 'completed'
        WHEN t.saga_status = 'failed' THEN 'failed'
        ELSE 
            CASE (t.id % 6)
                WHEN 0 THEN 'uninitialized'
                WHEN 1 THEN 'pending'
                WHEN 2 THEN 'running'
                WHEN 3 THEN 'completed'
                WHEN 4 THEN 'failed'
                ELSE 'delay'
            END
    END as compensation_status,
    
    -- 重试次数
    CASE WHEN t.saga_status = 'failed' THEN (t.id % 3) ELSE 0 END as retry_count,
    
    t.created_at,
    DATE_ADD(t.created_at, INTERVAL (step_info.step_index * 30) SECOND) as updated_at

FROM saga_transactions t
CROSS JOIN (
    SELECT 1 as step_index, 'CreateOrder' as action, 'order-service' as service_name, 'http://order-service:8080/saga/compensate' as compensate_endpoint
    UNION ALL SELECT 2, 'ProcessPayment', 'payment-service', 'http://payment-service:8080/saga/compensate'
    UNION ALL SELECT 3, 'ReserveInventory', 'inventory-service', 'http://inventory-service:8080/saga/compensate'
    UNION ALL SELECT 4, 'SendNotification', 'notification-service', 'http://notification-service:8080/saga/compensate'
    UNION ALL SELECT 5, 'UpdateUserPoints', 'user-service', 'http://user-service:8080/saga/compensate'
) step_info
WHERE step_info.step_index <= (
    CASE 
        WHEN (t.id % 5) = 0 THEN 5  -- 20% 的saga有5个步骤
        WHEN (t.id % 5) = 1 THEN 4  -- 20% 的saga有4个步骤
        ELSE 3                      -- 60% 的saga有3个步骤
    END
);

-- 更新表统计信息
ANALYZE TABLE saga_transactions;
ANALYZE TABLE saga_steps;

-- 数据统计报告
SELECT '=== 小规模测试数据初始化完成 ===' as status;

SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 数据比例验证
SELECT 
    CONCAT('数据比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2),
           ':1'
    ) as data_ratio;

-- 步骤分布
SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

-- 状态分布
SELECT 
    saga_status,
    COUNT(*) AS count
FROM saga_transactions 
GROUP BY saga_status
ORDER BY count DESC;

SELECT '✅ 小规模测试数据初始化完成！' as final_status;
