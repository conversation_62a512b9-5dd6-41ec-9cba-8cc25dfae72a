-- Saga 分布式事务系统 - 初始化测试数据脚本
-- 生成时间: 2025年7月31日
-- 目标: 创建符合业务约束的测试数据，事务:步骤比例 = 1:(3-5)

USE saga;

-- 清理现有测试数据
TRUNCATE TABLE saga_steps;
TRUNCATE TABLE saga_transactions;

-- 重置自增ID
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;

-- 设置变量
SET @batch_size = 10000;  -- 每批处理数量
SET @total_sagas = 50000; -- 总saga数量
SET @min_steps_per_saga = 3; -- 每个saga最少步骤数
SET @max_steps_per_saga = 5; -- 每个saga最多步骤数

-- 创建临时表用于生成数据
CREATE TEMPORARY TABLE temp_saga_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    saga_id CHAR(36) NOT NULL,
    name VARCHAR(64) NOT NULL,
    saga_status ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL,
    step_index_mode ENUM('manual', 'auto') NOT NULL,
    steps_count INT NOT NULL,
    business_type VARCHAR(20) NOT NULL
);

-- 生成Saga事务数据
INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type)
SELECT 
    -- 生成UUID格式的saga_id (36位标准UUID格式)
    CONCAT(
        SUBSTRING(MD5(CONCAT('saga', n, UNIX_TIMESTAMP())), 1, 8), '-',
        SUBSTRING(MD5(CONCAT('test', n, RAND())), 1, 4), '-',
        '4000', '-',
        SUBSTRING(MD5(CONCAT('data', n, NOW())), 1, 4), '-',
        SUBSTRING(MD5(CONCAT('init', n, CONNECTION_ID())), 1, 12)
    ) as saga_id,
    
    -- 生成业务相关的名称
    CASE (n % 5)
        WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n, 6, '0'))
        WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n, 6, '0'))
        WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n, 6, '0'))
        WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n, 6, '0'))
        ELSE CONCAT('数据同步流程-', LPAD(n, 6, '0'))
    END as name,
    
    -- 生成符合业务逻辑的状态分布
    CASE 
        WHEN (n % 100) < 60 THEN 'running'      -- 60% running
        WHEN (n % 100) < 80 THEN 'completed'    -- 20% completed  
        WHEN (n % 100) < 90 THEN 'pending'      -- 10% pending
        WHEN (n % 100) < 95 THEN 'compensating' -- 5% compensating
        ELSE 'failed'                           -- 5% failed
    END as saga_status,
    
    -- 步骤索引模式分布
    CASE WHEN (n % 3) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode,
    
    -- 每个saga的步骤数量 (3-5个)
    @min_steps_per_saga + (n % (@max_steps_per_saga - @min_steps_per_saga + 1)) as steps_count,
    
    -- 业务类型
    CASE (n % 5)
        WHEN 0 THEN 'ecommerce'
        WHEN 1 THEN 'payment'
        WHEN 2 THEN 'inventory'
        WHEN 3 THEN 'user'
        ELSE 'sync'
    END as business_type

FROM (
    SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 as n
    FROM 
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e
) numbers
WHERE n < @total_sagas;

-- 插入Saga事务数据
INSERT INTO saga_transactions (
    saga_id, name, saga_status, step_index_mode, 
    cur_step_index, compensation_window_sec, created_at, updated_at
)
SELECT 
    saga_id,
    name,
    saga_status,
    step_index_mode,
    CASE WHEN step_index_mode = 'auto' THEN steps_count ELSE 0 END as cur_step_index,
    CASE business_type
        WHEN 'ecommerce' THEN 300  -- 电商5分钟
        WHEN 'payment' THEN 600    -- 支付10分钟
        WHEN 'inventory' THEN 180  -- 库存3分钟
        WHEN 'user' THEN 120       -- 用户2分钟
        ELSE 240                   -- 其他4分钟
    END as compensation_window_sec,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND) as created_at, -- 最近2小时内创建
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) as updated_at  -- 最近1小时内更新
FROM temp_saga_data
ORDER BY id;

-- 显示Saga事务创建进度
SELECT CONCAT('已创建 ', COUNT(*), ' 个Saga事务') as progress FROM saga_transactions;

-- 创建步骤数据
-- 为每个saga创建对应数量的步骤，确保满足唯一约束 uq_saga_action_service

-- 定义业务步骤模板
CREATE TEMPORARY TABLE step_templates (
    step_order INT,
    action VARCHAR(64),
    service_name VARCHAR(100),
    business_type VARCHAR(20),
    compensate_endpoint VARCHAR(255),
    PRIMARY KEY (business_type, step_order)
);

-- 插入业务步骤模板
INSERT INTO step_templates VALUES
-- 电商订单流程 (5个步骤)
(1, 'CreateOrder', 'order-service', 'ecommerce', 'http://order-service:8080/saga/compensate/CancelOrder'),
(2, 'ProcessPayment', 'payment-service', 'ecommerce', 'http://payment-service:8080/saga/compensate/RefundPayment'),
(3, 'ReserveInventory', 'inventory-service', 'ecommerce', 'http://inventory-service:8080/saga/compensate/ReleaseInventory'),
(4, 'SendNotification', 'notification-service', 'ecommerce', 'http://notification-service:8080/saga/compensate/CancelNotification'),
(5, 'UpdateUserPoints', 'user-service', 'ecommerce', 'http://user-service:8080/saga/compensate/RevertPoints'),

-- 支付处理流程 (4个步骤)
(1, 'ValidateAccount', 'account-service', 'payment', 'http://account-service:8080/saga/compensate/UnlockAccount'),
(2, 'ProcessPayment', 'payment-service', 'payment', 'http://payment-service:8080/saga/compensate/RefundPayment'),
(3, 'UpdateBalance', 'account-service', 'payment', 'http://account-service:8080/saga/compensate/RevertBalance'),
(4, 'LogTransaction', 'audit-service', 'payment', 'http://audit-service:8080/saga/compensate/RemoveLog'),

-- 库存管理流程 (3个步骤)
(1, 'CheckInventory', 'inventory-service', 'inventory', 'http://inventory-service:8080/saga/compensate/UnlockInventory'),
(2, 'ReserveStock', 'inventory-service', 'inventory', 'http://inventory-service:8080/saga/compensate/ReleaseStock'),
(3, 'UpdateWarehouse', 'warehouse-service', 'inventory', 'http://warehouse-service:8080/saga/compensate/RevertWarehouse'),

-- 用户注册流程 (4个步骤)
(1, 'CreateUser', 'user-service', 'user', 'http://user-service:8080/saga/compensate/DeleteUser'),
(2, 'SendWelcomeEmail', 'notification-service', 'user', 'http://notification-service:8080/saga/compensate/CancelEmail'),
(3, 'InitializeProfile', 'profile-service', 'user', 'http://profile-service:8080/saga/compensate/DeleteProfile'),
(4, 'GrantPermissions', 'auth-service', 'user', 'http://auth-service:8080/saga/compensate/RevokePermissions'),

-- 数据同步流程 (3个步骤)
(1, 'ExtractData', 'etl-service', 'sync', 'http://etl-service:8080/saga/compensate/CleanupExtract'),
(2, 'TransformData', 'etl-service', 'sync', 'http://etl-service:8080/saga/compensate/RevertTransform'),
(3, 'LoadData', 'data-service', 'sync', 'http://data-service:8080/saga/compensate/RemoveData');

-- 批量生成步骤数据
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, retry_count, created_at, updated_at
)
SELECT
    -- 生成唯一的step_id (32位)
    MD5(CONCAT(t.saga_id, st.action, st.service_name, st.step_order, RAND())) as step_id,

    t.saga_id,
    st.action,
    st.step_order as step_index,
    st.service_name,

    -- 生成符合业务逻辑的context_data
    CASE st.business_type
        WHEN 'ecommerce' THEN JSON_OBJECT(
            'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, 1, 8)),
            'userId', CONCAT('user-', (CHAR_LENGTH(t.saga_id) % 10000) + 1),
            'amount', ROUND(99.99 + (CHAR_LENGTH(t.saga_id) % 900), 2),
            'productId', CONCAT('PROD-', (CHAR_LENGTH(t.saga_id) % 1000) + 1),
            'timestamp', UNIX_TIMESTAMP(NOW())
        )
        WHEN 'payment' THEN JSON_OBJECT(
            'paymentId', CONCAT('PAY-', SUBSTRING(t.saga_id, 1, 8)),
            'accountId', CONCAT('ACC-', (CHAR_LENGTH(t.saga_id) % 5000) + 1),
            'amount', ROUND(50.00 + (CHAR_LENGTH(t.saga_id) % 500), 2),
            'currency', 'CNY',
            'timestamp', UNIX_TIMESTAMP(NOW())
        )
        WHEN 'inventory' THEN JSON_OBJECT(
            'productId', CONCAT('PROD-', (CHAR_LENGTH(t.saga_id) % 1000) + 1),
            'warehouseId', CONCAT('WH-', (CHAR_LENGTH(t.saga_id) % 100) + 1),
            'quantity', (CHAR_LENGTH(t.saga_id) % 10) + 1,
            'reservationId', CONCAT('RSV-', SUBSTRING(t.saga_id, 1, 8)),
            'timestamp', UNIX_TIMESTAMP(NOW())
        )
        WHEN 'user' THEN JSON_OBJECT(
            'userId', CONCAT('user-', SUBSTRING(t.saga_id, 1, 8)),
            'email', CONCAT('user', CHAR_LENGTH(t.saga_id), '@example.com'),
            'username', CONCAT('user_', CHAR_LENGTH(t.saga_id)),
            'registrationId', CONCAT('REG-', SUBSTRING(t.saga_id, 1, 8)),
            'timestamp', UNIX_TIMESTAMP(NOW())
        )
        ELSE JSON_OBJECT(
            'syncId', CONCAT('SYNC-', SUBSTRING(t.saga_id, 1, 8)),
            'sourceTable', CONCAT('table_', (CHAR_LENGTH(t.saga_id) % 50) + 1),
            'batchSize', (CHAR_LENGTH(t.saga_id) % 1000) + 100,
            'timestamp', UNIX_TIMESTAMP(NOW())
        )
    END as context_data,

    -- 生成补偿上下文
    JSON_OBJECT(
        'sagaId', t.saga_id,
        'stepIndex', st.step_order,
        'action', st.action,
        'service', st.service_name,
        'reason', 'saga_rollback',
        'timestamp', UNIX_TIMESTAMP(NOW())
    ) as compensation_context,

    st.compensate_endpoint,

    -- 生成符合业务逻辑的补偿状态分布
    CASE
        WHEN t.saga_status = 'completed' THEN 'completed'
        WHEN t.saga_status = 'failed' THEN 'failed'
        WHEN t.saga_status = 'compensating' THEN
            CASE (CHAR_LENGTH(t.saga_id) % 4)
                WHEN 0 THEN 'running'
                WHEN 1 THEN 'completed'
                WHEN 2 THEN 'failed'
                ELSE 'pending'
            END
        ELSE
            CASE (CHAR_LENGTH(t.saga_id) % 6)
                WHEN 0 THEN 'uninitialized'
                WHEN 1 THEN 'pending'
                WHEN 2 THEN 'running'
                WHEN 3 THEN 'completed'
                WHEN 4 THEN 'failed'
                ELSE 'delay'
            END
    END as compensation_status,

    -- 重试次数 (失败状态的步骤可能有重试)
    CASE
        WHEN t.saga_status = 'failed' THEN (CHAR_LENGTH(t.saga_id) % 3)
        WHEN t.saga_status = 'compensating' THEN (CHAR_LENGTH(t.saga_id) % 2)
        ELSE 0
    END as retry_count,

    s.created_at,
    DATE_ADD(s.created_at, INTERVAL (st.step_order * 30) SECOND) as updated_at

FROM temp_saga_data t
JOIN saga_transactions s ON s.saga_id = t.saga_id
JOIN step_templates st ON st.business_type = t.business_type AND st.step_order <= t.steps_count
ORDER BY t.id, st.step_order;

-- 清理临时表
DROP TEMPORARY TABLE temp_saga_data;
DROP TEMPORARY TABLE step_templates;

-- 更新表统计信息
ANALYZE TABLE saga_transactions;
ANALYZE TABLE saga_steps;

-- 数据完整性验证和统计报告
SELECT '=== 数据初始化完成 ===' as status;

-- 基础数据统计
SELECT
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM saga_transactions
UNION ALL
SELECT
    'saga_steps' AS table_name,
    COUNT(*) AS record_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM saga_steps;

-- 数据比例验证
SELECT
    CONCAT('数据比例 - 步骤:事务 = ',
           (SELECT COUNT(*) FROM saga_steps),
           ':',
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2),
           ':1'
    ) as data_ratio;

-- Saga状态分布
SELECT
    '=== Saga状态分布 ===' as info;
SELECT
    saga_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) AS percentage
FROM saga_transactions
GROUP BY saga_status
ORDER BY count DESC;

-- 业务类型分布 (通过名称前缀识别)
SELECT
    '=== 业务类型分布 ===' as info;
SELECT
    CASE
        WHEN name LIKE '电商订单%' THEN 'ecommerce'
        WHEN name LIKE '支付处理%' THEN 'payment'
        WHEN name LIKE '库存管理%' THEN 'inventory'
        WHEN name LIKE '用户注册%' THEN 'user'
        ELSE 'sync'
    END as business_type,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) AS percentage
FROM saga_transactions
GROUP BY business_type
ORDER BY count DESC;

-- 步骤数量分布
SELECT
    '=== 每个Saga的步骤数量分布 ===' as info;
SELECT
    steps_per_saga,
    COUNT(*) AS saga_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) AS percentage
FROM (
    SELECT
        saga_id,
        COUNT(*) as steps_per_saga
    FROM saga_steps
    GROUP BY saga_id
) step_counts
GROUP BY steps_per_saga
ORDER BY steps_per_saga;

-- 步骤操作分布
SELECT
    '=== 步骤操作类型分布 ===' as info;
SELECT
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps
GROUP BY action
ORDER BY count DESC;

-- 服务分布
SELECT
    '=== 服务分布 ===' as info;
SELECT
    service_name,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps
GROUP BY service_name
ORDER BY count DESC;

-- 补偿状态分布
SELECT
    '=== 补偿状态分布 ===' as info;
SELECT
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps
GROUP BY compensation_status
ORDER BY count DESC;

-- 数据约束验证
SELECT
    '=== 数据约束验证 ===' as info;

-- 验证唯一约束 uq_saga_action_service
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN '✅ 唯一约束验证通过: 无重复的(saga_id, action, service_name)组合'
        ELSE CONCAT('❌ 唯一约束验证失败: 发现', COUNT(*), '个重复组合')
    END as constraint_check
FROM (
    SELECT saga_id, action, service_name, COUNT(*) as cnt
    FROM saga_steps
    GROUP BY saga_id, action, service_name
    HAVING cnt > 1
) duplicates;

-- 验证外键关系
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN '✅ 外键关系验证通过: 所有步骤都有对应的saga事务'
        ELSE CONCAT('❌ 外键关系验证失败: 发现', COUNT(*), '个孤立步骤')
    END as foreign_key_check
FROM saga_steps s
LEFT JOIN saga_transactions t ON s.saga_id = t.saga_id
WHERE t.saga_id IS NULL;

-- 验证JSON格式
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN '✅ JSON格式验证通过: 所有context_data和compensation_context都是有效JSON'
        ELSE CONCAT('❌ JSON格式验证失败: 发现', COUNT(*), '个无效JSON')
    END as json_format_check
FROM saga_steps
WHERE NOT JSON_VALID(context_data) OR NOT JSON_VALID(compensation_context);

-- 最终总结
SELECT
    '=== 初始化总结 ===' as summary;
SELECT
    CONCAT('✅ 成功创建 ',
           (SELECT COUNT(*) FROM saga_transactions),
           ' 个Saga事务和 ',
           (SELECT COUNT(*) FROM saga_steps),
           ' 个步骤数据') as result;

SELECT
    CONCAT('✅ 数据比例: ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 2),
           ':1 (目标范围: 3:1 - 5:1)') as ratio_result;

SELECT '✅ 数据初始化脚本执行完成！可以开始性能测试。' as final_status;
