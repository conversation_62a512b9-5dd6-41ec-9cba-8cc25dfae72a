#!/bin/bash

# 循环插入步骤数据脚本 - 确保3:1比例
# 为前10万个saga创建完整的3个步骤

set -e

echo "开始批量插入步骤数据..."
echo "目标：为前100,000个saga创建3个步骤，达到3:1比例"

# 获取没有步骤数据的saga列表
echo "获取需要处理的saga列表..."
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT saga_id FROM saga_transactions 
WHERE saga_id NOT IN (SELECT DISTINCT saga_id FROM saga_steps) 
ORDER BY created_at 
LIMIT 100000;" | tail -n +2 > /tmp/saga_list.txt

# 检查获取的saga数量
saga_count=$(wc -l < /tmp/saga_list.txt)
echo "获取到 $saga_count 个需要处理的saga"

if [ "$saga_count" -eq 0 ]; then
    echo "没有需要处理的saga"
    exit 0
fi

# 分批处理，每批1000个
batch_size=1000
total_batches=$(( (saga_count + batch_size - 1) / batch_size ))

echo "将分 $total_batches 批处理，每批 $batch_size 个saga"

for batch in $(seq 1 $total_batches); do
    start_line=$(( (batch - 1) * batch_size + 1 ))
    end_line=$(( batch * batch_size ))
    
    echo "处理第 $batch/$total_batches 批 (行 $start_line-$end_line)..."
    
    # 获取当前批次的saga列表
    sed -n "${start_line},${end_line}p" /tmp/saga_list.txt > /tmp/current_batch.txt
    
    # 为当前批次的每个saga创建3个步骤
    batch_num=0
    while IFS= read -r saga_id; do
        if [ -n "$saga_id" ]; then
            batch_num=$((batch_num + 1))
            order_id="ORDER-$(printf "%08d" $((start_line + batch_num - 1)))"
            payment_id="PAY-$(printf "%08d" $((start_line + batch_num - 1)))"
            product_id="PROD-$(printf "%04d" $((batch_num % 1000)))"
            
            # 插入3个步骤
            docker exec saga-mysql mysql -u root -p12345678a -e "
            USE saga;
            INSERT IGNORE INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status
            ) VALUES 
            (
                'create-$(echo -n \"${saga_id}1\" | md5sum | cut -c1-24)',
                '$saga_id',
                'CreateOrder',
                1,
                'order-service',
                '{\"orderId\": \"$order_id\", \"amount\": 299.99, \"userId\": \"user-$batch_num\"}',
                '{\"orderId\": \"$order_id\", \"reason\": \"saga_rollback\"}',
                'http://order-service:8080/saga/compensate',
                'uninitialized'
            ),
            (
                'payment-$(echo -n \"${saga_id}2\" | md5sum | cut -c1-22)',
                '$saga_id',
                'ProcessPayment',
                2,
                'payment-service',
                '{\"paymentId\": \"$payment_id\", \"amount\": 299.99, \"method\": \"alipay\"}',
                '{\"paymentId\": \"$payment_id\", \"reason\": \"saga_rollback\"}',
                'http://payment-service:8080/saga/compensate',
                'pending'
            ),
            (
                'inventory-$(echo -n \"${saga_id}3\" | md5sum | cut -c1-20)',
                '$saga_id',
                'ReserveInventory',
                3,
                'inventory-service',
                '{\"productId\": \"$product_id\", \"quantity\": 2, \"warehouseId\": \"WH-001\"}',
                '{\"productId\": \"$product_id\", \"reason\": \"saga_rollback\"}',
                'http://inventory-service:8080/saga/compensate',
                'completed'
            );" 2>/dev/null || echo "插入失败: $saga_id"
        fi
    done < /tmp/current_batch.txt
    
    # 显示进度
    if [ $((batch % 10)) -eq 0 ] || [ $batch -eq $total_batches ]; then
        current_steps=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
        echo "进度: $batch/$total_batches 批完成，当前步骤数: $current_steps"
    fi
    
    # 短暂休息，避免数据库压力过大
    sleep 0.1
done

# 清理临时文件
rm -f /tmp/saga_list.txt /tmp/current_batch.txt

# 显示最终统计
echo ""
echo "批量插入完成！显示最终统计..."
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    CONCAT('最终比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6),
           ':1'
    ) as final_ratio;

SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

SELECT 
    COUNT(*) as complete_sagas_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 4) as percentage_of_total
FROM (
    SELECT saga_id
    FROM saga_steps
    GROUP BY saga_id
    HAVING COUNT(DISTINCT action) = 3
) t;"

echo ""
echo "数据修复完成！"
echo "目标比例: 3:1 (步骤:事务)"
echo "处理的saga数量: $saga_count"
echo "预期新增步骤数: $((saga_count * 3))"
