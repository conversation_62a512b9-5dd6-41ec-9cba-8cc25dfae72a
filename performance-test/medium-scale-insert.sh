#!/bin/bash

# 中等规模插入 - 为10,000个saga创建步骤数据，达到更好的比例

set -e

echo "中等规模测试：为10,000个saga创建步骤数据..."

# 获取10,000个没有步骤数据的saga
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT saga_id FROM saga_transactions 
WHERE saga_id NOT IN (SELECT DISTINCT saga_id FROM saga_steps) 
ORDER BY created_at 
LIMIT 10000;" | tail -n +2 > /tmp/medium_saga_list.txt

# 检查获取的saga数量
saga_count=$(wc -l < /tmp/medium_saga_list.txt)
echo "获取到 $saga_count 个saga进行处理"

# 分批处理，每批50个
batch_size=50
total_batches=$(( (saga_count + batch_size - 1) / batch_size ))

echo "将分 $total_batches 批处理，每批 $batch_size 个saga"

for batch in $(seq 1 $total_batches); do
    start_line=$(( (batch - 1) * batch_size + 1 ))
    end_line=$(( batch * batch_size ))
    
    # 获取当前批次的saga列表
    sed -n "${start_line},${end_line}p" /tmp/medium_saga_list.txt > /tmp/current_medium_batch.txt
    
    # 为当前批次的每个saga创建3个步骤
    batch_counter=0
    while IFS= read -r saga_id; do
        if [ -n "$saga_id" ]; then
            batch_counter=$((batch_counter + 1))
            global_counter=$(( (batch - 1) * batch_size + batch_counter ))
            
            order_id="ORDER-$(printf "%08d" $global_counter)"
            payment_id="PAY-$(printf "%08d" $global_counter)"
            product_id="PROD-$(printf "%04d" $((global_counter % 1000)))"
            
            # 生成唯一的step_id
            create_step_id="med-create-$(printf "%08d" $global_counter)"
            payment_step_id="med-payment-$(printf "%08d" $global_counter)"
            inventory_step_id="med-inventory-$(printf "%08d" $global_counter)"
            
            # 插入3个步骤
            docker exec saga-mysql mysql -u root -p12345678a -e "
            USE saga;
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status
            ) VALUES 
            (
                '$create_step_id',
                '$saga_id',
                'CreateOrder',
                1,
                'order-service',
                '{\"orderId\": \"$order_id\", \"amount\": $(echo \"scale=2; 99.99 + ($global_counter % 900)\" | bc), \"userId\": \"user-$global_counter\", \"productId\": \"$product_id\"}',
                '{\"orderId\": \"$order_id\", \"reason\": \"saga_rollback\", \"service\": \"order-service\"}',
                'http://order-service:8080/saga/compensate',
                '$(case $((global_counter % 6)) in 0) echo "uninitialized";; 1) echo "pending";; 2) echo "running";; 3) echo "completed";; 4) echo "failed";; *) echo "delay";; esac)'
            ),
            (
                '$payment_step_id',
                '$saga_id',
                'ProcessPayment',
                2,
                'payment-service',
                '{\"paymentId\": \"$payment_id\", \"amount\": $(echo \"scale=2; 99.99 + ($global_counter % 900)\" | bc), \"method\": \"$(case $((global_counter % 3)) in 0) echo "alipay";; 1) echo "wechat";; *) echo "bank";; esac)\"}',
                '{\"paymentId\": \"$payment_id\", \"reason\": \"saga_rollback\", \"service\": \"payment-service\"}',
                'http://payment-service:8080/saga/compensate',
                '$(case $((global_counter % 6)) in 0) echo "uninitialized";; 1) echo "pending";; 2) echo "running";; 3) echo "completed";; 4) echo "failed";; *) echo "delay";; esac)'
            ),
            (
                '$inventory_step_id',
                '$saga_id',
                'ReserveInventory',
                3,
                'inventory-service',
                '{\"productId\": \"$product_id\", \"quantity\": $((global_counter % 10 + 1)), \"warehouseId\": \"WH-$(printf \"%03d\" $((global_counter % 100)))\", \"orderId\": \"$order_id\"}',
                '{\"productId\": \"$product_id\", \"reason\": \"saga_rollback\", \"service\": \"inventory-service\"}',
                'http://inventory-service:8080/saga/compensate',
                '$(case $((global_counter % 6)) in 0) echo "uninitialized";; 1) echo "pending";; 2) echo "running";; 3) echo "completed";; 4) echo "failed";; *) echo "delay";; esac)'
            );" 2>/dev/null
        fi
    done < /tmp/current_medium_batch.txt
    
    # 显示进度
    if [ $((batch % 20)) -eq 0 ] || [ $batch -eq $total_batches ]; then
        current_steps=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
        current_ratio=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6) as ratio;" 2>/dev/null | tail -1)
        echo "进度: $batch/$total_batches 批完成，当前步骤数: $current_steps，当前比例: $current_ratio:1"
    fi
    
    # 短暂休息
    sleep 0.05
done

# 清理临时文件
rm -f /tmp/medium_saga_list.txt /tmp/current_medium_batch.txt

# 显示最终统计
echo ""
echo "中等规模处理完成！显示最终统计..."
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    CONCAT('最终比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6),
           ':1'
    ) as final_ratio;

SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;

SELECT 
    COUNT(*) as complete_sagas_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 4) as percentage_of_total
FROM (
    SELECT saga_id
    FROM saga_steps
    GROUP BY saga_id
    HAVING COUNT(DISTINCT action) = 3
) t;"

echo ""
echo "中等规模处理完成！"
echo "处理的saga数量: $saga_count"
echo "预期新增步骤数: $((saga_count * 3))"
echo "目标：逐步接近3:1比例"
