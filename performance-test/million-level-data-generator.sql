-- 百万级测试数据生成脚本
-- 基于 performance-testing-plan.md 的 Level 2 (100万级) 规格

USE saga;

-- 创建测试数据生成存储过程
DELIMITER $$
CREATE PROCEDURE GenerateMillionLevelTestData(IN saga_count INT)
BEGIN
    DECLARE i INT DEFAULT 1;
    DECLARE saga_id VARCHAR(64);
    DECLARE step_count INT;
    DECLARE j INT;
    DECLARE batch_size INT DEFAULT 1000;
    DECLARE current_batch INT DEFAULT 0;

    -- 开始事务
    START TRANSACTION;

    WHILE i <= saga_count DO
        SET saga_id = CONCAT('perf-million-saga-', LPAD(i, 10, '0'));
        SET step_count = FLOOR(2 + RAND() * 4); -- 2-5个步骤

        -- 插入Saga事务
        INSERT INTO saga_transactions (
            saga_id, name, saga_status, step_index_mode,
            cur_step_index, compensation_window_sec, created_at, updated_at
        ) VALUES (
            saga_id,
            CONCAT('百万级性能测试事务_', i),
            CASE FLOOR(RAND() * 5)
                WHEN 0 THEN 'pending'
                WHEN 1 THEN 'running'
                WHEN 2 THEN 'completed'
                WHEN 3 THEN 'compensating'
                ELSE 'failed'
            END,
            'auto',
            step_count,
            300,
            DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
            NOW()
        );

        -- 插入步骤数据
        SET j = 1;
        WHILE j <= step_count DO
            INSERT INTO saga_steps (
                step_id, saga_id, action, step_index, service_name,
                context_data, compensation_context, compensate_endpoint,
                compensation_status, created_at, updated_at
            ) VALUES (
                CONCAT('step-', saga_id, '-', j),
                saga_id,
                CASE j
                    WHEN 1 THEN 'CreateOrder'
                    WHEN 2 THEN 'ProcessPayment'
                    WHEN 3 THEN 'ReserveInventory'
                    WHEN 4 THEN 'SendNotification'
                    ELSE 'UpdateStatus'
                END,
                j,
                CASE j
                    WHEN 1 THEN 'order-service'
                    WHEN 2 THEN 'payment-service'
                    WHEN 3 THEN 'inventory-service'
                    WHEN 4 THEN 'notification-service'
                    ELSE 'status-service'
                END,
                CONCAT('{"orderId":"ORDER-', i, '","amount":', (100 + RAND() * 900), ',"userId":"user-', FLOOR(RAND() * 10000), '"}'),
                CONCAT('{"orderId":"ORDER-', i, '","reason":"saga_rollback","step":', j, '}'),
                CASE j
                    WHEN 1 THEN 'http://order-service:8080/compensate'
                    WHEN 2 THEN 'http://payment-service:8080/compensate'
                    WHEN 3 THEN 'http://inventory-service:8080/compensate'
                    WHEN 4 THEN 'http://notification-service:8080/compensate'
                    ELSE 'http://status-service:8080/compensate'
                END,
                CASE FLOOR(RAND() * 6)
                    WHEN 0 THEN 'uninitialized'
                    WHEN 1 THEN 'pending'
                    WHEN 2 THEN 'running'
                    WHEN 3 THEN 'completed'
                    WHEN 4 THEN 'failed'
                    ELSE 'delay'
                END,
                DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
                NOW()
            );
            SET j = j + 1;
        END WHILE;

        SET i = i + 1;
        SET current_batch = current_batch + 1;

        -- 每1000条提交一次，避免事务过大
        IF current_batch >= batch_size THEN
            COMMIT;
            START TRANSACTION;
            SET current_batch = 0;
            
            -- 显示进度
            IF i % 10000 = 0 THEN
                SELECT CONCAT('已生成 ', i, ' 条 Saga 事务数据') AS progress;
            END IF;
        END IF;
    END WHILE;

    -- 提交最后一批
    COMMIT;
    
    SELECT CONCAT('百万级测试数据生成完成！总计生成 ', saga_count, ' 条 Saga 事务') AS result;
END$$
DELIMITER ;

-- 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_saga_status_created ON saga_transactions(saga_status, created_at);
CREATE INDEX IF NOT EXISTS idx_saga_name ON saga_transactions(name);
CREATE INDEX IF NOT EXISTS idx_step_saga_compensation ON saga_steps(saga_id, compensation_status);
CREATE INDEX IF NOT EXISTS idx_step_service_action ON saga_steps(service_name, action);
CREATE INDEX IF NOT EXISTS idx_step_created_at ON saga_steps(created_at);

-- 生成100万条测试数据
-- 注意：这个操作可能需要10-30分钟，取决于硬件性能
CALL GenerateMillionLevelTestData(1000000);

-- 分析表统计信息，优化查询性能
ANALYZE TABLE saga_transactions;
ANALYZE TABLE saga_steps;

-- 显示数据统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count,
    MIN(created_at) AS earliest_record,
    MAX(created_at) AS latest_record
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count,
    MIN(created_at) AS earliest_record,
    MAX(created_at) AS latest_record
FROM saga_steps;

-- 显示状态分布
SELECT 
    saga_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) AS percentage
FROM saga_transactions 
GROUP BY saga_status
ORDER BY count DESC;

SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;
