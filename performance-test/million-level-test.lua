-- 百万级性能测试脚本
-- 基于 saga-transactions-api.md 的真实 API 接口
-- 目标：Level 2 (100万级) 性能测试

local counter = 0
local thread_id = 0
local created_sagas = {}
local existing_saga_ids = {}
local max_saga_pool = 200

-- 预定义的测试数据模板
local service_templates = {
    {service = "order-service", action = "CreateOrder", endpoint = "http://order-service:8080/compensate"},
    {service = "payment-service", action = "ProcessPayment", endpoint = "http://payment-service:8080/compensate"},
    {service = "inventory-service", action = "ReserveInventory", endpoint = "http://inventory-service:8080/compensate"},
    {service = "notification-service", action = "SendNotification", endpoint = "http://notification-service:8080/compensate"},
    {service = "status-service", action = "UpdateStatus", endpoint = "http://status-service:8080/compensate"}
}

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    created_sagas = {}
    
    -- 预填充一些已存在的 saga ID（用于查询测试）
    for i = 1, 100 do
        local saga_id = string.format("perf-million-saga-%010d", math.random(1, 1000000))
        table.insert(existing_saga_ids, saga_id)
    end
    
    print(string.format("Thread %d initialized with %d existing saga IDs", thread_id, #existing_saga_ids))
end

-- 从响应中提取 sagaId
function extract_saga_id(body)
    if not body then return nil end
    local saga_id = string.match(body, '"sagaId":"([^"]+)"')
    return saga_id
end

-- 获取可用的 saga ID
function get_available_saga_id()
    if #created_sagas > 0 then
        local index = (counter % #created_sagas) + 1
        return created_sagas[index]
    elseif #existing_saga_ids > 0 then
        local index = (counter % #existing_saga_ids) + 1
        return existing_saga_ids[index]
    end
    return nil
end

-- 生成真实的业务数据
function generate_context_data(action, order_id)
    if action == "CreateOrder" then
        return string.format('{"orderId":"%s","userId":"user-%d","amount":%.2f,"productId":"PROD-%d"}', 
            order_id, math.random(1, 10000), 99.99 + math.random() * 900, math.random(1, 1000))
    elseif action == "ProcessPayment" then
        return string.format('{"orderId":"%s","paymentId":"PAY-%d","amount":%.2f,"method":"alipay"}', 
            order_id, math.random(100000, 999999), 99.99 + math.random() * 900)
    elseif action == "ReserveInventory" then
        return string.format('{"orderId":"%s","productId":"PROD-%d","quantity":%d,"warehouseId":"WH-%d"}', 
            order_id, math.random(1, 1000), math.random(1, 10), math.random(1, 100))
    elseif action == "SendNotification" then
        return string.format('{"orderId":"%s","userId":"user-%d","type":"order_confirmation","channel":"email"}', 
            order_id, math.random(1, 10000))
    else
        return string.format('{"orderId":"%s","status":"processing","timestamp":"%d"}', 
            order_id, os.time())
    end
end

-- 生成补偿上下文数据
function generate_compensation_context(action, order_id)
    return string.format('{"orderId":"%s","action":"%s","reason":"saga_rollback","timestamp":"%d"}', 
        order_id, action, os.time())
end

-- 请求生成函数
function request()
    counter = counter + 1
    local operation_type = counter % 10
    
    if operation_type <= 2 then
        -- 30% 概率创建新的 Saga 事务（基于真实 API）
        local saga_name = string.format("百万级性能测试事务_%d_%d", thread_id, counter)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "auto"
        }]], saga_name)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions")
        
    elseif operation_type <= 5 then
        -- 30% 概率上报补偿信息（基于真实 API）
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        local template = service_templates[math.random(1, #service_templates)]
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local context_data = generate_context_data(template.action, order_id)
        local compensation_context = generate_compensation_context(template.action, order_id)
        
        local body = string.format([[{
            "sagaId": "%s",
            "action": "%s",
            "serviceName": "%s",
            "contextData": "%s",
            "compensationContext": "%s",
            "compensateEndpoint": "%s"
        }]], saga_id, template.action, template.service, 
            context_data:gsub('"', '\\"'), compensation_context:gsub('"', '\\"'), template.endpoint)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif operation_type <= 7 then
        -- 20% 概率查询事务状态（基于真实 API）
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
        
    elseif operation_type <= 8 then
        -- 10% 概率提交事务（基于真实 API）
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
        
    else
        -- 10% 概率回滚事务（基于真实 API）
        local saga_id = get_available_saga_id()
        if not saga_id then
            return create_new_saga()
        end
        
        local body = string.format([[{
            "sagaId": "%s",
            "failReason": "百万级性能测试 - 模拟业务失败",
            "failedStep": "ProcessPayment",
            "executionMode": "sync"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/rollback")
    end
end

-- 创建新 saga 的辅助函数
function create_new_saga()
    local saga_name = string.format("fallback-saga-%d-%d", thread_id, counter)
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto"
    }]], saga_name)
    
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    return wrk.format(nil, "/saga/transactions")
end

-- 响应处理函数
function response(status, headers, body)
    if status == 200 then
        -- 如果是创建事务的成功响应，提取并保存 sagaId
        if wrk.method == "POST" and string.find(wrk.path, "/saga/transactions$") then
            local saga_id = extract_saga_id(body)
            if saga_id and #created_sagas < max_saga_pool then
                table.insert(created_sagas, saga_id)
            end
        end
    elseif status == 404 then
        -- 404 是正常的（事务不存在）
    elseif status >= 400 then
        -- 记录其他错误
        print(string.format("Thread %d - Error %d: %s", thread_id, status, body or ""))
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("==============================\n")
    io.write("百万级性能测试结果 (Level 2):\n")
    io.write("==============================\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("线程 %d 管理的 Saga 数量: %d\n", thread_id, #created_sagas))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("==============================\n")
    io.write("百万级业务场景模拟:\n")
    io.write("- 创建事务: 30% (基于真实 API)\n")
    io.write("- 补偿上报: 30% (5种真实服务)\n")
    io.write("- 状态查询: 20% (基于真实 sagaId)\n")
    io.write("- 事务提交: 10% (基于真实 API)\n")
    io.write("- 事务回滚: 10% (基于真实 API)\n")
    io.write("特点: 100% 基于真实 API 和业务数据\n")
    io.write("数据规模: 100万级 Saga 事务\n")
    io.write("==============================\n")
end
