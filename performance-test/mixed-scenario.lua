-- Saga 混合场景性能测试脚本
-- 模拟真实业务场景：创建事务 -> 上报补偿 -> 提交事务

local counter = 0
local thread_id = 0
local created_sagas = {}

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
end

-- 请求生成函数
function request()
    counter = counter + 1
    local request_type = counter % 3
    
    if request_type == 1 then
        -- 创建 Saga 事务
        local saga_name = string.format("mixed-test-saga-%d-%d", thread_id, counter)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "auto"
        }]], saga_name)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions")
        
    elseif request_type == 2 then
        -- 上报补偿信息
        local saga_id = string.format("mixed-saga-%d-%d", thread_id, math.floor(counter/3))
        local body = string.format([[{
            "sagaId": "%s",
            "action": "ProcessOrder-%d",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%d\"}",
            "compensationContext": "{\"orderId\":\"%d\",\"reason\":\"cancel\"}",
            "compensateEndpoint": "http://order-service:8080/compensate"
        }]], saga_id, counter, counter, counter)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    else
        -- 提交事务
        local saga_id = string.format("mixed-saga-%d-%d", thread_id, math.floor(counter/3))
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
    end
end

-- 响应处理函数
function response(status, headers, body)
    if status ~= 200 then
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("Saga 混合场景性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
end
