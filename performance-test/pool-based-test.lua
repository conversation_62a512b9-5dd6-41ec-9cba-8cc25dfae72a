-- 基于预创建 Saga 池的性能测试脚本
-- 使用真实的 sagaId 进行完整的业务流程测试

local saga_ids = {}
local counter = 0
local thread_id = 0

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    
    -- 读取预创建的 saga IDs
    local pool_file = os.getenv("SAGA_POOL_FILE")
    if not pool_file then
        -- 尝试查找最新的 saga 池文件
        local handle = io.popen("ls -t performance-test/results/saga_pool_*.txt 2>/dev/null | head -1")
        if handle then
            pool_file = handle:read("*a"):gsub("%s+", "")
            handle:close()
        end
    end
    
    if pool_file and pool_file ~= "" then
        local file = io.open(pool_file, "r")
        if file then
            for line in file:lines() do
                if line and line ~= "" then
                    table.insert(saga_ids, line)
                end
            end
            file:close()
            print(string.format("Thread %d loaded %d saga IDs from %s", thread_id, #saga_ids, pool_file))
        else
            print(string.format("Thread %d: Cannot open file %s", thread_id, pool_file))
        end
    else
        print(string.format("Thread %d: No saga pool file found", thread_id))
    end
end

-- 获取随机的 saga ID
function get_saga_id()
    if #saga_ids == 0 then
        return nil
    end
    local index = (counter % #saga_ids) + 1
    return saga_ids[index]
end

-- 请求生成函数
function request()
    counter = counter + 1
    
    if #saga_ids == 0 then
        -- 如果没有可用的 saga ID，执行健康检查
        wrk.method = "GET"
        wrk.body = ""
        return wrk.format(nil, "/hello")
    end
    
    local operation_type = counter % 5
    local saga_id = get_saga_id()
    
    if operation_type == 1 then
        -- 20% 上报订单服务补偿信息
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "CreateOrder",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%s\",\"userId\":\"user-%d\",\"amount\":299.99,\"productId\":\"PROD-%d\"}",
            "compensationContext": "{\"orderId\":\"%s\",\"reason\":\"cancel_order\",\"refundAmount\":299.99}",
            "compensateEndpoint": "http://order-service:8080/orders/compensate"
        }]], saga_id, order_id, counter, counter % 1000, order_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif operation_type == 2 then
        -- 20% 上报支付服务补偿信息
        local payment_id = string.format("PAY-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "ProcessPayment",
            "serviceName": "payment-service",
            "contextData": "{\"paymentId\":\"%s\",\"amount\":299.99,\"currency\":\"CNY\",\"method\":\"alipay\"}",
            "compensationContext": "{\"paymentId\":\"%s\",\"refundAmount\":299.99,\"reason\":\"saga_rollback\"}",
            "compensateEndpoint": "http://payment-service:8080/payments/compensate"
        }]], saga_id, payment_id, payment_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif operation_type == 3 then
        -- 20% 查询事务状态
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
        
    elseif operation_type == 4 then
        -- 20% 提交事务
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
        
    else
        -- 20% 回滚事务
        local body = string.format([[{
            "sagaId": "%s",
            "failReason": "业务规则验证失败 - 库存不足",
            "failedStep": "ProcessPayment",
            "executionMode": "sync"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/rollback")
    end
end

-- 响应处理函数
function response(status, headers, body)
    -- 记录不同状态码的统计
    if status == 200 then
        -- 成功响应
    elseif status == 404 then
        -- 事务不存在（可能已被删除，这是正常的）
    elseif status >= 400 then
        -- 其他错误
        print(string.format("Thread %d - Error %d: %s", thread_id, status, body or ""))
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("基于真实 Saga 池的性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("线程 %d 使用的 Saga 池大小: %d\n", thread_id, #saga_ids))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("真实业务场景模拟:\n")
    io.write("- 订单补偿上报: 20%\n")
    io.write("- 支付补偿上报: 20%\n")
    io.write("- 事务状态查询: 20%\n")
    io.write("- 事务提交: 20%\n")
    io.write("- 事务回滚: 20%\n")
    io.write("特点: 100% 基于真实 sagaId 的业务操作\n")
    io.write("优势: 真实反映生产环境的性能表现\n")
    io.write("------------------------------\n")
end
