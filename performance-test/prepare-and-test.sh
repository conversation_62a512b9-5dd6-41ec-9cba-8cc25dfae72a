#!/bin/bash

# Saga 分布式事务系统 - 基于真实 sagaId 的性能测试脚本
# 先预创建一批 saga 事务，然后基于真实 sagaId 进行测试

set -e

# 配置参数
BASE_URL="http://localhost:8080"
SAGA_POOL_SIZE=1000
RESULTS_DIR="performance-test/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SAGA_IDS_FILE="$RESULTS_DIR/saga_ids_$TIMESTAMP.txt"

# 创建结果目录
mkdir -p "$RESULTS_DIR"

echo "=========================================="
echo "基于真实 sagaId 的 Saga 性能测试"
echo "时间: $(date)"
echo "=========================================="

# 检查服务是否可用
echo "检查服务状态..."
if ! curl -s "$BASE_URL/hello" > /dev/null; then
    echo "错误: Saga 服务不可用，请检查服务状态"
    exit 1
fi
echo "✅ 服务状态正常"

# 第一阶段：预创建 Saga 事务池
echo ""
echo "第一阶段：预创建 Saga 事务池"
echo "----------------------------------------"
echo "正在创建 $SAGA_POOL_SIZE 个 Saga 事务..."

# 清空 saga IDs 文件
> "$SAGA_IDS_FILE"

# 批量创建 saga 事务
for i in $(seq 1 $SAGA_POOL_SIZE); do
    if [ $((i % 100)) -eq 0 ]; then
        echo "已创建 $i/$SAGA_POOL_SIZE 个事务..."
    fi
    
    # 创建 saga 事务
    response=$(curl -s -X POST "$BASE_URL/saga/transactions" \
        -H "Content-Type: application/json" \
        -d "{\"name\": \"perf-test-saga-$i\", \"stepIndexMode\": \"auto\"}")
    
    # 提取 sagaId
    saga_id=$(echo "$response" | grep -o '"sagaId":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$saga_id" ]; then
        echo "$saga_id" >> "$SAGA_IDS_FILE"
    else
        echo "警告: 第 $i 个事务创建失败"
    fi
done

# 检查创建的事务数量
created_count=$(wc -l < "$SAGA_IDS_FILE")
echo "✅ 成功创建 $created_count 个 Saga 事务"

if [ "$created_count" -lt 100 ]; then
    echo "错误: 创建的事务数量太少，无法进行有效测试"
    exit 1
fi

# 第二阶段：基于真实 sagaId 的性能测试
echo ""
echo "第二阶段：基于真实 sagaId 的性能测试"
echo "----------------------------------------"

# 创建基于真实 sagaId 的测试脚本
cat > "performance-test/real-saga-test.lua" << 'EOF'
-- 基于真实 sagaId 的性能测试脚本

local saga_ids = {}
local counter = 0
local thread_id = 0

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    
    -- 读取预创建的 saga IDs
    local file = io.open("performance-test/results/saga_ids_TIMESTAMP.txt", "r")
    if file then
        for line in file:lines() do
            if line and line ~= "" then
                table.insert(saga_ids, line)
            end
        end
        file:close()
    end
    
    print(string.format("Thread %d loaded %d saga IDs", thread_id, #saga_ids))
end

-- 请求生成函数
function request()
    counter = counter + 1
    
    if #saga_ids == 0 then
        -- 如果没有可用的 saga ID，返回健康检查请求
        wrk.method = "GET"
        wrk.body = ""
        return wrk.format(nil, "/hello")
    end
    
    local request_type = counter % 4
    local saga_index = ((counter - 1) % #saga_ids) + 1
    local saga_id = saga_ids[saga_index]
    
    if request_type == 1 then
        -- 上报补偿信息
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "CreateOrder",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%s\",\"amount\":299.99}",
            "compensationContext": "{\"orderId\":\"%s\",\"reason\":\"cancel_order\"}",
            "compensateEndpoint": "http://order-service:8080/compensate"
        }]], saga_id, order_id, order_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif request_type == 2 then
        -- 查询事务状态
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
        
    elseif request_type == 3 then
        -- 提交事务
        local body = string.format([[{
            "sagaId": "%s"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/commit")
        
    else
        -- 回滚事务
        local body = string.format([[{
            "sagaId": "%s",
            "failReason": "测试回滚场景",
            "executionMode": "none"
        }]], saga_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/rollback")
    end
end

-- 响应处理函数
function response(status, headers, body)
    if status ~= 200 and status ~= 404 then
        print("Error response: " .. status)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("基于真实 sagaId 的性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("使用的 Saga 数量: %d\n", #saga_ids))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("操作分布: 补偿上报 25%, 状态查询 25%, 事务提交 25%, 事务回滚 25%\n")
    io.write("基于真实 sagaId 的完整业务流程测试\n")
    io.write("------------------------------\n")
end
EOF

# 替换时间戳
sed -i.bak "s/TIMESTAMP/$TIMESTAMP/g" "performance-test/real-saga-test.lua"
rm "performance-test/real-saga-test.lua.bak"

# 执行性能测试
echo "2.1 基于真实 sagaId 的混合操作测试"
wrk -t8 -c50 -d60s --latency -s performance-test/real-saga-test.lua "$BASE_URL" > "$RESULTS_DIR/real_saga_test_$TIMESTAMP.txt"
echo "✅ 真实 sagaId 测试完成"

# 第三阶段：清理测试数据（可选）
echo ""
echo "第三阶段：测试数据清理"
echo "----------------------------------------"
read -p "是否清理测试数据？(y/N): " cleanup_choice

if [[ "$cleanup_choice" =~ ^[Yy]$ ]]; then
    echo "正在清理测试数据..."
    
    # 这里可以添加清理逻辑，比如删除创建的 saga 事务
    # 注意：实际生产环境中要谨慎执行删除操作
    
    echo "⚠️  注意：请手动清理数据库中的测试数据"
    echo "测试 saga 名称模式: perf-test-saga-*"
else
    echo "保留测试数据，可用于后续分析"
fi

echo ""
echo "=========================================="
echo "基于真实 sagaId 的性能测试完成！"
echo "结果文件: $RESULTS_DIR/real_saga_test_$TIMESTAMP.txt"
echo "Saga IDs 文件: $SAGA_IDS_FILE"
echo "时间: $(date)"
echo "=========================================="

# 显示测试结果摘要
echo ""
echo "测试结果摘要:"
echo "----------------------------------------"
if [ -f "$RESULTS_DIR/real_saga_test_$TIMESTAMP.txt" ]; then
    echo "QPS: $(grep -E "Requests/sec:" "$RESULTS_DIR/real_saga_test_$TIMESTAMP.txt" | head -1)"
    echo "延迟: $(grep -E "Latency.*avg" "$RESULTS_DIR/real_saga_test_$TIMESTAMP.txt" | head -1)"
fi
