-- 状态查询性能测试脚本 (使用真实sagaId)
-- 业务约束: 查询真实存在的saga事务状态

wrk.method = "GET"
wrk.headers["Content-Type"] = "application/json"

-- 读取所有状态的sagaId用于查询测试
local saga_ids = {}

-- 加载running状态的sagaId
local running_file = io.open("performance-test/results/running_saga_ids.txt", "r")
if running_file then
    for line in running_file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    running_file:close()
end

-- 加载pending状态的sagaId
local pending_file = io.open("performance-test/results/pending_saga_ids.txt", "r")
if pending_file then
    for line in pending_file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    pending_file:close()
end

print("加载了 " .. #saga_ids .. " 个真实sagaId用于状态查询测试")

local counter = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 状态查询请求 (GET /saga/transactions/{sagaId})
    local path = "/saga/transactions/" .. saga_id
    
    return wrk.format("GET", path, nil, nil)
end

function done(summary, latency, requests)
    print("\n=== 状态查询接口性能测试结果 ===")
    print(string.format("使用真实sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", summary.requests - summary.errors.connect - summary.errors.read - summary.errors.write - summary.errors.status - summary.errors.timeout))
    print(string.format("错误数: %d", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
end
