-- 状态查询性能测试脚本 (使用真实sagaId)
wrk.method = "GET"
wrk.headers["Content-Type"] = "application/json"

-- ⚠️ 重要：使用从数据库获取的真实sagaId列表
-- 这些sagaId都是通过创建事务接口获取的真实存在的ID
local saga_ids = {
    "rws1w001000dbq982q6j886sndh544aw",
    "rws1w001000dbq982q6qagysnh9i1rhi",
    "rws1w001000dbq982q7inpzsni3cojl8",
    "rws1w001000dbq982q7rnygsnk3prjf1",
    "rws1w001000dbq982q5vmaysnbeydnwr",
    "rws1w001000dbq982q6lzbasneckzukt",
    "rws1w001000dbq982q8c1lbsnodndo7p",
    "rws1w001000dbq982q7teucsnmvafqsu",
    "rws1w001000dbq982q7t5ohsnl45y460",
    "rws1w001000dbq982q8c723snp8fvo87",
    "rws1w001000dbq982q5q4iksnaonvpqz",
    "rws1w001000dbq982q5n7zusn8mgrrs9",
    "rws1w001000dbq982q6eyu6sncw1ngjj",
    "rws1w001000dbq982q6okrvsnfpd59pd",
    "rws1w001000dbq982q6q94csng449jvf",
    "rws1w001000dbq982q7y39zsnnmpsz6j",
    "rws1w001000dbq982q57owjsn752qu0n",
    "rws1w001000dbq982q5q0y3sn9tyhlgf",
    "rws1w001000dbq982q7paq5snj8zex41"
}

-- 请求计数器
local counter = 0

function init(args)
    counter = 0
end

function request()
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    local path = "/saga/transactions/" .. saga_id
    return wrk.format("GET", path)
end

function response(status, headers, body)
    if status ~= 200 then
        print("Error: " .. status .. " - " .. body)
    end
end
