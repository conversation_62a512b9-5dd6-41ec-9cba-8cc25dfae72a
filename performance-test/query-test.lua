-- 状态查询性能测试脚本
wrk.method = "GET"
wrk.headers["Content-Type"] = "application/json"

-- 预定义的sagaId列表 (使用数据库中的真实ID)
local saga_ids = {
    "rws1w001000dbq8wrigslbdpzmbppr5u",
    "rws1w001000dbq8wrj2pywxpzn1lfvzm", 
    "rws1w001000dbq8wrl2vlexpzoj5wx0k",
    "rws1w001000dbq8wrn1su9vpzpmkdag7",
    "rws1w001000dbq8wrp6ftxhpzq65ucdt"
}

-- 请求计数器
local counter = 0

function init(args)
    counter = 0
end

function request()
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    local path = "/saga/transactions/" .. saga_id
    return wrk.format("GET", path)
end

function response(status, headers, body)
    if status ~= 200 then
        print("Error: " .. status .. " - " .. body)
    end
end
