#!/bin/bash

# 快速测试插入 - 为100个saga创建步骤数据

set -e

echo "快速测试：为100个saga创建步骤数据..."

# 获取100个没有步骤数据的saga
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT saga_id FROM saga_transactions 
WHERE saga_id NOT IN (SELECT DISTINCT saga_id FROM saga_steps) 
ORDER BY created_at 
LIMIT 100;" | tail -n +2 > /tmp/test_saga_list.txt

# 检查获取的saga数量
saga_count=$(wc -l < /tmp/test_saga_list.txt)
echo "获取到 $saga_count 个saga进行测试"

# 为每个saga创建3个步骤
counter=0
while IFS= read -r saga_id; do
    if [ -n "$saga_id" ]; then
        counter=$((counter + 1))
        order_id="ORDER-$(printf "%08d" $counter)"
        payment_id="PAY-$(printf "%08d" $counter)"
        product_id="PROD-$(printf "%04d" $((counter % 1000)))"
        
        # 生成唯一的step_id
        create_step_id="quick-create-$(printf "%06d" $counter)"
        payment_step_id="quick-payment-$(printf "%06d" $counter)"
        inventory_step_id="quick-inventory-$(printf "%06d" $counter)"
        
        # 插入3个步骤
        docker exec saga-mysql mysql -u root -p12345678a -e "
        USE saga;
        INSERT INTO saga_steps (
            step_id, saga_id, action, step_index, service_name,
            context_data, compensation_context, compensate_endpoint,
            compensation_status
        ) VALUES 
        (
            '$create_step_id',
            '$saga_id',
            'CreateOrder',
            1,
            'order-service',
            '{\"orderId\": \"$order_id\", \"amount\": 299.99, \"userId\": \"user-$counter\"}',
            '{\"orderId\": \"$order_id\", \"reason\": \"saga_rollback\"}',
            'http://order-service:8080/saga/compensate',
            'uninitialized'
        ),
        (
            '$payment_step_id',
            '$saga_id',
            'ProcessPayment',
            2,
            'payment-service',
            '{\"paymentId\": \"$payment_id\", \"amount\": 299.99, \"method\": \"alipay\"}',
            '{\"paymentId\": \"$payment_id\", \"reason\": \"saga_rollback\"}',
            'http://payment-service:8080/saga/compensate',
            'pending'
        ),
        (
            '$inventory_step_id',
            '$saga_id',
            'ReserveInventory',
            3,
            'inventory-service',
            '{\"productId\": \"$product_id\", \"quantity\": 2, \"warehouseId\": \"WH-001\"}',
            '{\"productId\": \"$product_id\", \"reason\": \"saga_rollback\"}',
            'http://inventory-service:8080/saga/compensate',
            'completed'
        );" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "成功为saga $counter 创建步骤数据"
        else
            echo "失败：saga $counter"
        fi
        
        # 每10个显示一次进度
        if [ $((counter % 10)) -eq 0 ]; then
            current_steps=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
            echo "进度: $counter/$saga_count，当前步骤数: $current_steps"
        fi
    fi
done < /tmp/test_saga_list.txt

# 清理临时文件
rm -f /tmp/test_saga_list.txt

# 显示最终统计
echo ""
echo "快速测试完成！显示统计结果..."
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT COUNT(*) as total_sagas FROM saga_transactions;
SELECT COUNT(*) as total_steps FROM saga_steps;
SELECT action, COUNT(*) as count FROM saga_steps GROUP BY action ORDER BY count DESC;
SELECT ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6) as current_ratio;"

echo ""
echo "快速测试完成！处理了 $counter 个saga，预期新增 $((counter * 3)) 个步骤"
