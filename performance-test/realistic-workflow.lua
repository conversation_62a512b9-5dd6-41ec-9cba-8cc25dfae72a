-- 真实业务工作流性能测试脚本
-- 基于实际创建的 sagaId 进行完整的业务流程测试

local json = require("json")
local counter = 0
local thread_id = 0
local created_sagas = {}
local saga_pool_size = 100  -- 每个线程维护的 saga 池大小

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
    
    -- 初始化 saga 池
    created_sagas = {}
    
    print(string.format("Thread %d initialized", thread_id))
end

-- 解析 JSON 响应
function parse_json_response(body)
    local success, result = pcall(function()
        local decoded = json.decode(body)
        if decoded and decoded.data and decoded.data.sagaId then
            return decoded.data.sagaId
        end
        return nil
    end)
    
    if success then
        return result
    else
        return nil
    end
end

-- 请求生成函数
function request()
    counter = counter + 1
    local request_type = counter % 5  -- 5步完整流程
    
    if request_type == 1 then
        -- 步骤1: 创建 Saga 事务
        local saga_name = string.format("realistic-workflow-%d-%d", thread_id, counter)
        local body = string.format([[{
            "name": "%s",
            "stepIndexMode": "auto"
        }]], saga_name)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions")
        
    elseif request_type == 2 then
        -- 步骤2: 上报第一个服务的补偿信息
        local saga_id = get_random_saga_id()
        if not saga_id then
            -- 如果没有可用的 saga，创建一个新的
            return create_fallback_saga()
        end
        
        local order_id = string.format("ORDER-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "CreateOrder",
            "serviceName": "order-service",
            "contextData": "{\"orderId\":\"%s\",\"userId\":\"user-%d\",\"amount\":299.99}",
            "compensationContext": "{\"orderId\":\"%s\",\"reason\":\"cancel_order\"}",
            "compensateEndpoint": "http://order-service:8080/orders/compensate"
        }]], saga_id, order_id, counter, order_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif request_type == 3 then
        -- 步骤3: 上报第二个服务的补偿信息
        local saga_id = get_random_saga_id()
        if not saga_id then
            return create_fallback_saga()
        end
        
        local payment_id = string.format("PAY-%d-%d", thread_id, counter)
        local body = string.format([[{
            "sagaId": "%s",
            "action": "ProcessPayment",
            "serviceName": "payment-service",
            "contextData": "{\"paymentId\":\"%s\",\"amount\":299.99,\"currency\":\"CNY\"}",
            "compensationContext": "{\"paymentId\":\"%s\",\"refundAmount\":299.99,\"reason\":\"saga_rollback\"}",
            "compensateEndpoint": "http://payment-service:8080/payments/compensate"
        }]], saga_id, payment_id, payment_id)
        
        wrk.method = "POST"
        wrk.body = body
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/compensation")
        
    elseif request_type == 4 then
        -- 步骤4: 查询事务状态
        local saga_id = get_random_saga_id()
        if not saga_id then
            return create_fallback_saga()
        end
        
        wrk.method = "GET"
        wrk.body = ""
        wrk.headers["Content-Type"] = "application/json"
        return wrk.format(nil, "/saga/transactions/" .. saga_id)
        
    else
        -- 步骤5: 提交或回滚事务 (90% 提交, 10% 回滚)
        local saga_id = get_random_saga_id()
        if not saga_id then
            return create_fallback_saga()
        end
        
        if counter % 10 == 0 then
            -- 10% 概率回滚
            local body = string.format([[{
                "sagaId": "%s",
                "failReason": "业务规则验证失败",
                "failedStep": "ProcessPayment",
                "executionMode": "sync"
            }]], saga_id)
            
            wrk.method = "POST"
            wrk.body = body
            wrk.headers["Content-Type"] = "application/json"
            return wrk.format(nil, "/saga/transactions/rollback")
        else
            -- 90% 概率提交
            local body = string.format([[{
                "sagaId": "%s"
            }]], saga_id)
            
            wrk.method = "POST"
            wrk.body = body
            wrk.headers["Content-Type"] = "application/json"
            return wrk.format(nil, "/saga/transactions/commit")
        end
    end
end

-- 获取随机的 saga ID
function get_random_saga_id()
    if #created_sagas == 0 then
        return nil
    end
    
    local index = (counter % #created_sagas) + 1
    return created_sagas[index]
end

-- 创建备用 saga（当池为空时）
function create_fallback_saga()
    local saga_name = string.format("fallback-saga-%d-%d", thread_id, counter)
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto"
    }]], saga_name)
    
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    return wrk.format(nil, "/saga/transactions")
end

-- 响应处理函数
function response(status, headers, body)
    if status == 200 then
        -- 如果是创建事务的响应，提取 sagaId
        if wrk.method == "POST" and string.find(wrk.path, "/saga/transactions$") then
            local saga_id = parse_json_response(body)
            if saga_id then
                -- 将新创建的 sagaId 添加到池中
                if #created_sagas < saga_pool_size then
                    table.insert(created_sagas, saga_id)
                else
                    -- 池满时，随机替换一个
                    local replace_index = (counter % saga_pool_size) + 1
                    created_sagas[replace_index] = saga_id
                end
            end
        end
    elseif status ~= 404 then
        -- 404 是正常的（事务不存在），其他错误需要记录
        print("Error response: " .. status .. " - " .. (body or ""))
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("真实业务工作流性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write(string.format("线程 %d 创建的 Saga 数量: %d\n", thread_id, #created_sagas))
    io.write("业务流程: 创建事务 -> 订单补偿 -> 支付补偿 -> 查询状态 -> 提交/回滚\n")
    io.write("提交率: 90%, 回滚率: 10%\n")
    io.write("基于真实 sagaId 的完整业务流程测试\n")
    io.write("------------------------------\n")
end
