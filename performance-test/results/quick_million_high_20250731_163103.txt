Thread 1 initialized with 100 existing saga IDs
Thread 2 initialized with 200 existing saga IDs
Thread 3 initialized with 300 existing saga IDs
Thread 4 initialized with 400 existing saga IDs
Thread 5 initialized with 500 existing saga IDs
Thread 6 initialized with 600 existing saga IDs
Thread 7 initialized with 700 existing saga IDs
Thread 8 initialized with 800 existing saga IDs
Thread 9 initialized with 900 existing saga IDs
Thread 10 initialized with 1000 existing saga IDs
Thread 11 initialized with 1100 existing saga IDs
Thread 12 initialized with 1200 existing saga IDs
Thread 13 initialized with 1300 existing saga IDs
Thread 14 initialized with 1400 existing saga IDs
Thread 15 initialized with 1500 existing saga IDs
Thread 16 initialized with 1600 existing saga IDs
Running 2m test @ http://localhost:8080
  16 threads and 100 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency    10.68ms    6.74ms  87.36ms   71.36%
    Req/Sec   586.89     60.40   818.00     69.65%
  Latency Distribution
     50%    9.51ms
     75%   14.17ms
     90%   19.50ms
     99%   32.28ms
  1122276 requests in 2.00m, 391.77MB read
Requests/sec:   9344.73
Transfer/sec:      3.26MB
==============================
百万级性能测试结果 (Level 2):
==============================
请求总数: 1122276
线程 16 管理的 Saga 数量: 0
总耗时: 120.10 秒
平均 QPS: 9344.73
错误数: 0
平均延迟: 10.68 ms
50% 延迟: 9.51 ms
90% 延迟: 19.50 ms
99% 延迟: 32.28 ms
==============================
百万级业务场景模拟:
- 创建事务: 30% (基于真实 API)
- 补偿上报: 30% (5种真实服务)
- 状态查询: 20% (基于真实 sagaId)
- 事务提交: 10% (基于真实 API)
- 事务回滚: 10% (基于真实 API)
特点: 100% 基于真实 API 和业务数据
数据规模: 100万级 Saga 事务
==============================
