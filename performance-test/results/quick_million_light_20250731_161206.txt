Thread 1 initialized with 100 existing saga IDs
Thread 2 initialized with 200 existing saga IDs
Thread 3 initialized with 300 existing saga IDs
Thread 4 initialized with 400 existing saga IDs
Running 2m test @ http://localhost:8080
  4 threads and 20 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency    22.37ms   30.66ms 390.16ms   83.49%
    Req/Sec   492.27    527.63     1.89k    73.23%
  Latency Distribution
     50%    5.83ms
     75%   32.91ms
     90%   68.98ms
     99%  133.20ms
  216400 requests in 1.85m, 82.01MB read
Requests/sec:   1950.33
Transfer/sec:    756.84KB
==============================
百万级性能测试结果 (Level 2):
==============================
请求总数: 216400
线程 4 管理的 Saga 数量: 0
总耗时: 110.96 秒
平均 QPS: 1950.33
错误数: 0
平均延迟: 22.37 ms
50% 延迟: 5.83 ms
90% 延迟: 68.98 ms
99% 延迟: 133.20 ms
==============================
百万级业务场景模拟:
- 创建事务: 30% (基于真实 API)
- 补偿上报: 30% (5种真实服务)
- 状态查询: 20% (基于真实 sagaId)
- 事务提交: 10% (基于真实 API)
- 事务回滚: 10% (基于真实 API)
特点: 100% 基于真实 API 和业务数据
数据规模: 100万级 Saga 事务
==============================
