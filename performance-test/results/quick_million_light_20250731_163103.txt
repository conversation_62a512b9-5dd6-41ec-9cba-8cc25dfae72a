Thread 1 initialized with 100 existing saga IDs
Thread 2 initialized with 200 existing saga IDs
Thread 3 initialized with 300 existing saga IDs
Thread 4 initialized with 400 existing saga IDs
Running 2m test @ http://localhost:8080
  4 threads and 20 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     7.20ms    4.84ms  57.95ms   75.47%
    Req/Sec   706.75     93.46     0.94k    73.30%
  Latency Distribution
     50%    6.21ms
     75%    9.23ms
     90%   13.34ms
     99%   23.67ms
  197877 requests in 1.89m, 70.15MB read
Requests/sec:   1745.02
Transfer/sec:    633.45KB
==============================
百万级性能测试结果 (Level 2):
==============================
请求总数: 197877
线程 4 管理的 Saga 数量: 0
总耗时: 113.40 秒
平均 QPS: 1745.02
错误数: 0
平均延迟: 7.20 ms
50% 延迟: 6.21 ms
90% 延迟: 13.34 ms
99% 延迟: 23.67 ms
==============================
百万级业务场景模拟:
- 创建事务: 30% (基于真实 API)
- 补偿上报: 30% (5种真实服务)
- 状态查询: 20% (基于真实 sagaId)
- 事务提交: 10% (基于真实 API)
- 事务回滚: 10% (基于真实 API)
特点: 100% 基于真实 API 和业务数据
数据规模: 100万级 Saga 事务
==============================
