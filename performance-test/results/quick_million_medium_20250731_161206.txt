Thread 1 initialized with 100 existing saga IDs
Thread 2 initialized with 200 existing saga IDs
Thread 3 initialized with 300 existing saga IDs
Thread 4 initialized with 400 existing saga IDs
Thread 5 initialized with 500 existing saga IDs
Thread 6 initialized with 600 existing saga IDs
Thread 7 initialized with 700 existing saga IDs
Thread 8 initialized with 800 existing saga IDs
Running 2m test @ http://localhost:8080
  8 threads and 50 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     9.96ms    9.90ms 159.79ms   84.58%
    Req/Sec   796.32    110.43     1.26k    85.48%
  Latency Distribution
     50%    5.60ms
     75%   10.75ms
     90%   26.70ms
     99%   40.93ms
  599838 requests in 1.58m, 209.53MB read
Requests/sec:   6339.66
Transfer/sec:      2.21MB
==============================
百万级性能测试结果 (Level 2):
==============================
请求总数: 599838
线程 8 管理的 Saga 数量: 0
总耗时: 94.62 秒
平均 QPS: 6339.66
错误数: 0
平均延迟: 9.96 ms
50% 延迟: 5.60 ms
90% 延迟: 26.70 ms
99% 延迟: 40.93 ms
==============================
百万级业务场景模拟:
- 创建事务: 30% (基于真实 API)
- 补偿上报: 30% (5种真实服务)
- 状态查询: 20% (基于真实 sagaId)
- 事务提交: 10% (基于真实 API)
- 事务回滚: 10% (基于真实 API)
特点: 100% 基于真实 API 和业务数据
数据规模: 100万级 Saga 事务
==============================
