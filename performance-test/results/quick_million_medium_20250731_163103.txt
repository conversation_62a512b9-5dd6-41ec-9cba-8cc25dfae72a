Thread 1 initialized with 100 existing saga IDs
Thread 2 initialized with 200 existing saga IDs
Thread 3 initialized with 300 existing saga IDs
Thread 4 initialized with 400 existing saga IDs
Thread 5 initialized with 500 existing saga IDs
Thread 6 initialized with 600 existing saga IDs
Thread 7 initialized with 700 existing saga IDs
Thread 8 initialized with 800 existing saga IDs
Running 2m test @ http://localhost:8080
  8 threads and 50 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency    11.53ms    8.33ms 114.78ms   70.38%
    Req/Sec   557.57     72.12     1.29k    68.92%
  Latency Distribution
     50%   10.14ms
     75%   15.71ms
     90%   22.38ms
     99%   38.25ms
  532917 requests in 2.00m, 188.50MB read
Requests/sec:   4438.35
Transfer/sec:      1.57MB
==============================
百万级性能测试结果 (Level 2):
==============================
请求总数: 532917
线程 8 管理的 Saga 数量: 0
总耗时: 120.07 秒
平均 QPS: 4438.35
错误数: 0
平均延迟: 11.53 ms
50% 延迟: 10.14 ms
90% 延迟: 22.38 ms
99% 延迟: 38.25 ms
==============================
百万级业务场景模拟:
- 创建事务: 30% (基于真实 API)
- 补偿上报: 30% (5种真实服务)
- 状态查询: 20% (基于真实 sagaId)
- 事务提交: 10% (基于真实 API)
- 事务回滚: 10% (基于真实 API)
特点: 100% 基于真实 API 和业务数据
数据规模: 100万级 Saga 事务
==============================
