-- Saga 事务回滚性能测试脚本
-- 测试 POST /saga/transactions/rollback 接口

local counter = 0
local thread_id = 0
local execution_modes = {"none", "sync", "async"}

-- 线程初始化
function setup(thread)
    thread_id = thread_id + 1
    thread:set("id", thread_id)
end

-- 请求生成函数
function request()
    counter = counter + 1
    
    -- 循环使用不同的执行模式
    local mode_index = (counter % #execution_modes) + 1
    local execution_mode = execution_modes[mode_index]
    
    -- 生成测试用的 Saga ID
    local saga_id = string.format("rollback-test-saga-%d-%d", thread_id, counter)
    
    -- 构建请求体
    local body = string.format([[{
        "sagaId": "%s",
        "failReason": "性能测试模拟失败-%d",
        "failedStep": "TestStep-%d",
        "executionMode": "%s"
    }]], saga_id, counter, counter, execution_mode)
    
    -- 设置请求头
    wrk.method = "POST"
    wrk.body = body
    wrk.headers["Content-Type"] = "application/json"
    
    return wrk.format(nil, "/saga/transactions/rollback")
end

-- 响应处理函数
function response(status, headers, body)
    if status ~= 200 and status ~= 404 then
        -- 404 是正常的，因为我们使用的是测试 Saga ID
        print("Error response: " .. status .. " - " .. body)
    end
end

-- 测试完成后的统计
function done(summary, latency, requests)
    io.write("------------------------------\n")
    io.write("Saga 事务回滚性能测试结果:\n")
    io.write("------------------------------\n")
    io.write(string.format("请求总数: %d\n", summary.requests))
    io.write(string.format("总耗时: %.2f 秒\n", summary.duration / 1000000))
    io.write(string.format("平均 QPS: %.2f\n", summary.requests / (summary.duration / 1000000)))
    io.write(string.format("错误数: %d\n", summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout))
    io.write(string.format("平均延迟: %.2f ms\n", latency.mean / 1000))
    io.write(string.format("50%% 延迟: %.2f ms\n", latency:percentile(50) / 1000))
    io.write(string.format("90%% 延迟: %.2f ms\n", latency:percentile(90) / 1000))
    io.write(string.format("99%% 延迟: %.2f ms\n", latency:percentile(99) / 1000))
    io.write("------------------------------\n")
    io.write("测试模式: none/sync/async 轮换\n")
    io.write("注意: 404 响应是正常的，因为使用的是测试 ID\n")
    io.write("------------------------------\n")
end
