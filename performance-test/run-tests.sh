#!/bin/bash

# Saga 分布式事务系统性能测试脚本
# 使用 wrk 工具进行压力测试

set -e

# 配置参数
BASE_URL="http://localhost:8080"
RESULTS_DIR="performance-test/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 创建结果目录
mkdir -p "$RESULTS_DIR"

echo "=========================================="
echo "Saga 分布式事务系统性能测试开始"
echo "时间: $(date)"
echo "=========================================="

# 检查服务是否可用
echo "检查服务状态..."
if ! curl -s "$BASE_URL/hello" > /dev/null; then
    echo "错误: Saga 服务不可用，请检查服务状态"
    exit 1
fi
echo "✅ 服务状态正常"

# 1. 基础功能性能测试
echo ""
echo "1. 开始基础功能性能测试..."
echo "----------------------------------------"

# 1.1 健康检查接口测试
echo "1.1 健康检查接口性能测试"
wrk -t4 -c10 -d30s --latency "$BASE_URL/hello" > "$RESULTS_DIR/health_check_$TIMESTAMP.txt"
echo "✅ 健康检查测试完成"

# 1.2 Saga 事务创建测试
echo "1.2 Saga 事务创建性能测试"
wrk -t4 -c10 -d30s --latency -s performance-test/create-saga.lua "$BASE_URL" > "$RESULTS_DIR/create_saga_$TIMESTAMP.txt"
echo "✅ 事务创建测试完成"

# 2. 并发性能测试
echo ""
echo "2. 开始并发性能测试..."
echo "----------------------------------------"

# 2.1 低并发测试 (10 并发)
echo "2.1 低并发测试 (10 并发)"
wrk -t4 -c10 -d60s --latency -s performance-test/create-saga.lua "$BASE_URL" > "$RESULTS_DIR/concurrent_low_$TIMESTAMP.txt"
echo "✅ 低并发测试完成"

# 2.2 中等并发测试 (50 并发)
echo "2.2 中等并发测试 (50 并发)"
wrk -t8 -c50 -d60s --latency -s performance-test/create-saga.lua "$BASE_URL" > "$RESULTS_DIR/concurrent_medium_$TIMESTAMP.txt"
echo "✅ 中等并发测试完成"

# 2.3 高并发测试 (100 并发)
echo "2.3 高并发测试 (100 并发)"
wrk -t12 -c100 -d60s --latency -s performance-test/create-saga.lua "$BASE_URL" > "$RESULTS_DIR/concurrent_high_$TIMESTAMP.txt"
echo "✅ 高并发测试完成"

# 3. 混合场景测试
echo ""
echo "3. 开始混合场景测试..."
echo "----------------------------------------"
wrk -t8 -c50 -d120s --latency -s performance-test/mixed-scenario.lua "$BASE_URL" > "$RESULTS_DIR/mixed_scenario_$TIMESTAMP.txt"
echo "✅ 混合场景测试完成"

# 4. 其他接口性能测试
echo ""
echo "4. 开始其他接口性能测试..."
echo "----------------------------------------"

# 4.1 Manual 模式事务创建测试
echo "4.1 Manual 模式事务创建测试"
wrk -t4 -c20 -d30s --latency -s performance-test/manual-mode-saga.lua "$BASE_URL" > "$RESULTS_DIR/manual_mode_saga_$TIMESTAMP.txt"
echo "✅ Manual 模式测试完成"

# 4.2 事务查询接口测试
echo "4.2 事务查询接口测试"
wrk -t4 -c20 -d30s --latency -s performance-test/get-saga-transaction.lua "$BASE_URL" > "$RESULTS_DIR/get_saga_transaction_$TIMESTAMP.txt"
echo "✅ 事务查询测试完成"

# 4.3 事务回滚接口测试
echo "4.3 事务回滚接口测试"
wrk -t4 -c20 -d30s --latency -s performance-test/rollback-saga.lua "$BASE_URL" > "$RESULTS_DIR/rollback_saga_$TIMESTAMP.txt"
echo "✅ 事务回滚测试完成"

# 4.4 电商工作流测试
echo "4.4 电商完整工作流测试"
wrk -t6 -c30 -d60s --latency -s performance-test/ecommerce-workflow.lua "$BASE_URL" > "$RESULTS_DIR/ecommerce_workflow_$TIMESTAMP.txt"
echo "✅ 电商工作流测试完成"

# 5. 压力测试
echo ""
echo "5. 开始压力测试..."
echo "----------------------------------------"
echo "5.1 极限并发测试 (200 并发)"
wrk -t16 -c200 -d60s --latency -s performance-test/create-saga.lua "$BASE_URL" > "$RESULTS_DIR/stress_test_$TIMESTAMP.txt"
echo "✅ 压力测试完成"

echo ""
echo "=========================================="
echo "性能测试完成！"
echo "结果文件保存在: $RESULTS_DIR/"
echo "时间: $(date)"
echo "=========================================="

# 生成测试报告摘要
echo ""
echo "测试结果摘要:"
echo "----------------------------------------"
for file in "$RESULTS_DIR"/*_"$TIMESTAMP".txt; do
    if [ -f "$file" ]; then
        echo "文件: $(basename "$file")"
        echo "QPS: $(grep -E "Requests/sec:|平均 QPS:" "$file" | head -1)"
        echo "延迟: $(grep -E "Latency.*avg|平均延迟:" "$file" | head -1)"
        echo "----------------------------------------"
    fi
done
