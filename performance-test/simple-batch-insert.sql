-- 简单批量插入步骤数据
USE saga;

-- 为前1万个saga插入CreateOrder步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('create-', SUBSTRING(MD5(saga_id), 1, 8)) as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    '{"orderId": "ORDER-001", "amount": 299.99}' as context_data,
    '{"orderId": "ORDER-001", "reason": "saga_rollback"}' as compensation_context,
    'http://order-service:8080/compensate' as compensate_endpoint,
    'uninitialized' as compensation_status
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'CreateOrder'
)
LIMIT 10000;

-- 为前1万个saga插入ProcessPayment步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('payment-', SUBSTRING(MD5(saga_id), 1, 8)) as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    '{"paymentId": "PAY-001", "amount": 299.99}' as context_data,
    '{"paymentId": "PAY-001", "reason": "saga_rollback"}' as compensation_context,
    'http://payment-service:8080/compensate' as compensate_endpoint,
    'pending' as compensation_status
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ProcessPayment'
)
LIMIT 10000;

-- 为前1万个saga插入ReserveInventory步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('inventory-', SUBSTRING(MD5(saga_id), 1, 8)) as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    '{"productId": "PROD-001", "quantity": 2}' as context_data,
    '{"productId": "PROD-001", "reason": "saga_rollback"}' as compensation_context,
    'http://inventory-service:8080/compensate' as compensate_endpoint,
    'completed' as compensation_status
FROM saga_transactions 
WHERE saga_id NOT IN (
    SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ReserveInventory'
)
LIMIT 10000;

-- 显示结果
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;
