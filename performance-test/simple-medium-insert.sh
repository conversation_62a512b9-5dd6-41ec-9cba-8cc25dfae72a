#!/bin/bash

# 简化的中等规模插入 - 为5,000个saga创建步骤数据

set -e

echo "简化中等规模测试：为5,000个saga创建步骤数据..."

# 获取5,000个没有步骤数据的saga
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT saga_id FROM saga_transactions 
WHERE saga_id NOT IN (SELECT DISTINCT saga_id FROM saga_steps) 
ORDER BY created_at 
LIMIT 5000;" | tail -n +2 > /tmp/simple_saga_list.txt

# 检查获取的saga数量
saga_count=$(wc -l < /tmp/simple_saga_list.txt)
echo "获取到 $saga_count 个saga进行处理"

# 为每个saga创建3个步骤
counter=0
while IFS= read -r saga_id; do
    if [ -n "$saga_id" ]; then
        counter=$((counter + 1))
        order_id="ORDER-$(printf "%08d" $counter)"
        payment_id="PAY-$(printf "%08d" $counter)"
        product_id="PROD-$(printf "%04d" $((counter % 1000)))"
        
        # 生成唯一的step_id
        create_step_id="simple-create-$(printf "%08d" $counter)"
        payment_step_id="simple-payment-$(printf "%08d" $counter)"
        inventory_step_id="simple-inventory-$(printf "%08d" $counter)"
        
        # 计算状态
        status_num=$((counter % 6))
        case $status_num in
            0) status="uninitialized";;
            1) status="pending";;
            2) status="running";;
            3) status="completed";;
            4) status="failed";;
            *) status="delay";;
        esac
        
        # 计算支付方式
        payment_num=$((counter % 3))
        case $payment_num in
            0) method="alipay";;
            1) method="wechat";;
            *) method="bank";;
        esac
        
        # 计算金额
        amount=$(echo "scale=2; 99.99 + ($counter % 900)" | bc)
        
        # 插入3个步骤
        docker exec saga-mysql mysql -u root -p12345678a -e "
        USE saga;
        INSERT INTO saga_steps (
            step_id, saga_id, action, step_index, service_name,
            context_data, compensation_context, compensate_endpoint,
            compensation_status
        ) VALUES 
        (
            '$create_step_id',
            '$saga_id',
            'CreateOrder',
            1,
            'order-service',
            '{\"orderId\": \"$order_id\", \"amount\": $amount, \"userId\": \"user-$counter\", \"productId\": \"$product_id\"}',
            '{\"orderId\": \"$order_id\", \"reason\": \"saga_rollback\", \"service\": \"order-service\"}',
            'http://order-service:8080/saga/compensate',
            '$status'
        ),
        (
            '$payment_step_id',
            '$saga_id',
            'ProcessPayment',
            2,
            'payment-service',
            '{\"paymentId\": \"$payment_id\", \"amount\": $amount, \"method\": \"$method\", \"orderId\": \"$order_id\"}',
            '{\"paymentId\": \"$payment_id\", \"reason\": \"saga_rollback\", \"service\": \"payment-service\"}',
            'http://payment-service:8080/saga/compensate',
            '$status'
        ),
        (
            '$inventory_step_id',
            '$saga_id',
            'ReserveInventory',
            3,
            'inventory-service',
            '{\"productId\": \"$product_id\", \"quantity\": $((counter % 10 + 1)), \"warehouseId\": \"WH-$(printf \"%03d\" $((counter % 100)))\", \"orderId\": \"$order_id\"}',
            '{\"productId\": \"$product_id\", \"reason\": \"saga_rollback\", \"service\": \"inventory-service\"}',
            'http://inventory-service:8080/saga/compensate',
            '$status'
        );" 2>/dev/null
        
        # 每100个显示一次进度
        if [ $((counter % 100)) -eq 0 ]; then
            current_steps=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT COUNT(*) FROM saga_steps;" 2>/dev/null | tail -1)
            current_ratio=$(docker exec saga-mysql mysql -u root -p12345678a -e "USE saga; SELECT ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6) as ratio;" 2>/dev/null | tail -1)
            echo "进度: $counter/$saga_count，当前步骤数: $current_steps，当前比例: $current_ratio:1"
        fi
    fi
done < /tmp/simple_saga_list.txt

# 清理临时文件
rm -f /tmp/simple_saga_list.txt

# 显示最终统计
echo ""
echo "简化中等规模处理完成！显示最终统计..."
docker exec saga-mysql mysql -u root -p12345678a -e "
USE saga;
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

SELECT 
    CONCAT('最终比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6),
           ':1'
    ) as final_ratio;

SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

SELECT 
    COUNT(*) as complete_sagas_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 4) as percentage_of_total
FROM (
    SELECT saga_id
    FROM saga_steps
    GROUP BY saga_id
    HAVING COUNT(DISTINCT action) = 3
) t;"

echo ""
echo "简化中等规模处理完成！"
echo "处理的saga数量: $counter"
echo "预期新增步骤数: $((counter * 3))"
