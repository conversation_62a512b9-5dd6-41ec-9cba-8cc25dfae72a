-- 智能修复3:1比例 - 为缺少步骤的saga补充数据
USE saga;

-- 分析当前步骤数据分布
SELECT 'Current step distribution:' as info;
SELECT 
    saga_id,
    GROUP_CONCAT(action ORDER BY step_index) as existing_actions,
    COUNT(*) as step_count
FROM saga_steps 
GROUP BY saga_id
ORDER BY step_count DESC;

-- 为缺少CreateOrder步骤的saga添加CreateOrder
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('create-', SUBSTRING(MD5(CONCAT(t.saga_id, 'create')), 1, 24)) as step_id,
    t.saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'amount', 299.99,
        'userId', CONCAT('user-', (CHAR_LENGTH(t.saga_id) % 10000) + 1),
        'productId', CONCAT('PROD-', (CHAR_LENGTH(t.saga_id) % 1000) + 1)
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'reason', 'saga_rollback',
        'service', 'order-service'
    ) as compensation_context,
    'http://order-service:8080/saga/compensate' as compensate_endpoint,
    'uninitialized' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'CreateOrder'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 为缺少ProcessPayment步骤的saga添加ProcessPayment
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('payment-', SUBSTRING(MD5(CONCAT(t.saga_id, 'payment')), 1, 22)) as step_id,
    t.saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'paymentId', CONCAT('PAY-', SUBSTRING(t.saga_id, -8)),
        'amount', 299.99,
        'method', CASE (CHAR_LENGTH(t.saga_id) % 3) WHEN 0 THEN 'alipay' WHEN 1 THEN 'wechat' ELSE 'bank' END
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'paymentId', CONCAT('PAY-', SUBSTRING(t.saga_id, -8)),
        'reason', 'saga_rollback',
        'service', 'payment-service'
    ) as compensation_context,
    'http://payment-service:8080/saga/compensate' as compensate_endpoint,
    'pending' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ProcessPayment'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 为缺少ReserveInventory步骤的saga添加ReserveInventory
INSERT IGNORE INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('inventory-', SUBSTRING(MD5(CONCAT(t.saga_id, 'inventory')), 1, 20)) as step_id,
    t.saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'productId', CONCAT('PROD-', (CHAR_LENGTH(t.saga_id) % 1000) + 1),
        'quantity', (CHAR_LENGTH(t.saga_id) % 10) + 1,
        'warehouseId', CONCAT('WH-', (CHAR_LENGTH(t.saga_id) % 100) + 1)
    ) as context_data,
    JSON_OBJECT(
        'orderId', CONCAT('ORDER-', SUBSTRING(t.saga_id, -8)),
        'productId', CONCAT('PROD-', (CHAR_LENGTH(t.saga_id) % 1000) + 1),
        'reason', 'saga_rollback',
        'service', 'inventory-service'
    ) as compensation_context,
    'http://inventory-service:8080/saga/compensate' as compensate_endpoint,
    'completed' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    WHERE saga_id NOT IN (
        SELECT DISTINCT saga_id FROM saga_steps WHERE action = 'ReserveInventory'
    )
    ORDER BY created_at
    LIMIT 10000
) t;

-- 显示修复后的统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示当前比例
SELECT 
    CONCAT('修复后比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 6),
           ':1'
    ) as fixed_ratio;

-- 显示步骤分布
SELECT 
    action,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;

-- 显示补偿状态分布
SELECT 
    compensation_status,
    COUNT(*) AS count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) AS percentage
FROM saga_steps 
GROUP BY compensation_status
ORDER BY count DESC;

-- 显示有完整3个步骤的saga数量
SELECT 
    CONCAT('有完整3个步骤的saga数量: ', COUNT(*)) as complete_sagas
FROM (
    SELECT saga_id
    FROM saga_steps
    GROUP BY saga_id
    HAVING COUNT(DISTINCT action) = 3
) t;
