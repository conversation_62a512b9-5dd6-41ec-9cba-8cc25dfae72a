-- 分步骤修复数据 - 逐步建立3:1比例
USE saga;

-- 第一步：清理测试数据
DELETE FROM saga_steps WHERE step_id NOT LIKE 'e255b7fc%';

-- 第二步：为前1000个saga创建CreateOrder步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('order-', SUBSTRING(MD5(saga_id), 1, 24)) as step_id,
    saga_id,
    'CreateOrder' as action,
    1 as step_index,
    'order-service' as service_name,
    '{"orderId": "ORDER-001", "amount": 299.99, "userId": "user-1001"}' as context_data,
    '{"orderId": "ORDER-001", "reason": "saga_rollback"}' as compensation_context,
    'http://order-service:8080/saga/compensate' as compensate_endpoint,
    'uninitialized' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    ORDER BY created_at 
    LIMIT 1000
) t;

-- 显示第一步结果
SELECT 'Step 1 - CreateOrder' as step_name, COUNT(*) as count FROM saga_steps WHERE action = 'CreateOrder';

-- 第三步：为前1000个saga创建ProcessPayment步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('pay-', SUBSTRING(MD5(saga_id), 1, 26)) as step_id,
    saga_id,
    'ProcessPayment' as action,
    2 as step_index,
    'payment-service' as service_name,
    '{"paymentId": "PAY-001", "amount": 299.99, "method": "alipay"}' as context_data,
    '{"paymentId": "PAY-001", "reason": "saga_rollback"}' as compensation_context,
    'http://payment-service:8080/saga/compensate' as compensate_endpoint,
    'pending' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    ORDER BY created_at 
    LIMIT 1000
) t;

-- 显示第二步结果
SELECT 'Step 2 - ProcessPayment' as step_name, COUNT(*) as count FROM saga_steps WHERE action = 'ProcessPayment';

-- 第四步：为前1000个saga创建ReserveInventory步骤
INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status
)
SELECT 
    CONCAT('inv-', SUBSTRING(MD5(saga_id), 1, 27)) as step_id,
    saga_id,
    'ReserveInventory' as action,
    3 as step_index,
    'inventory-service' as service_name,
    '{"productId": "PROD-001", "quantity": 2, "warehouseId": "WH-001"}' as context_data,
    '{"productId": "PROD-001", "reason": "saga_rollback"}' as compensation_context,
    'http://inventory-service:8080/saga/compensate' as compensate_endpoint,
    'completed' as compensation_status
FROM (
    SELECT saga_id 
    FROM saga_transactions 
    ORDER BY created_at 
    LIMIT 1000
) t;

-- 显示第三步结果
SELECT 'Step 3 - ReserveInventory' as step_name, COUNT(*) as count FROM saga_steps WHERE action = 'ReserveInventory';

-- 显示最终统计
SELECT 
    'saga_transactions' AS table_name,
    COUNT(*) AS record_count
FROM saga_transactions
UNION ALL
SELECT 
    'saga_steps' AS table_name,
    COUNT(*) AS record_count
FROM saga_steps;

-- 显示比例
SELECT 
    CONCAT('当前比例 - 步骤:事务 = ', 
           (SELECT COUNT(*) FROM saga_steps), 
           ':', 
           (SELECT COUNT(*) FROM saga_transactions),
           ' ≈ ',
           ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 3),
           ':1'
    ) as current_ratio;

-- 显示步骤分布
SELECT 
    action,
    COUNT(*) AS count
FROM saga_steps 
GROUP BY action
ORDER BY count DESC;
