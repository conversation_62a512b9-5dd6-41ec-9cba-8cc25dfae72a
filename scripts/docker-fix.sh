#!/bin/bash

# Docker 网络问题诊断和修复脚本

echo "🔍 Docker 网络问题诊断和修复工具"
echo "=================================="

# 检查 Docker 状态
echo "1. 检查 Docker 状态..."
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请启动 Docker Desktop"
    exit 1
fi
echo "✅ Docker 运行正常"

# 检查网络连接
echo ""
echo "2. 检查网络连接..."
if ping -c 1 registry-1.docker.io > /dev/null 2>&1; then
    echo "✅ Docker Hub 网络连接正常"
else
    echo "⚠️  Docker Hub 网络连接异常"
fi

# 检查 DNS 解析
echo ""
echo "3. 检查 DNS 解析..."
if nslookup registry-1.docker.io > /dev/null 2>&1; then
    echo "✅ DNS 解析正常"
else
    echo "⚠️  DNS 解析异常"
fi

# 清理 Docker 缓存
echo ""
echo "4. 清理 Docker 缓存..."
echo "清理未使用的镜像..."
docker image prune -f > /dev/null 2>&1
echo "清理未使用的容器..."
docker container prune -f > /dev/null 2>&1
echo "清理未使用的网络..."
docker network prune -f > /dev/null 2>&1
echo "✅ Docker 缓存清理完成"

# 重启 Docker 网络
echo ""
echo "5. 重启 Docker 网络..."
docker network ls | grep bridge > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Docker 网络正常"
else
    echo "⚠️  Docker 网络异常，尝试重启..."
fi

# 测试镜像拉取
echo ""
echo "6. 测试镜像拉取..."
echo "尝试拉取 alpine 镜像进行测试..."
if docker pull alpine:latest > /dev/null 2>&1; then
    echo "✅ 镜像拉取测试成功"
    docker rmi alpine:latest > /dev/null 2>&1
else
    echo "❌ 镜像拉取测试失败"
    echo ""
    echo "🛠️  建议的解决方案："
    echo "1. 检查网络连接"
    echo "2. 重启 Docker Desktop"
    echo "3. 配置 Docker 镜像源（如果在中国大陆）"
    echo "4. 检查防火墙设置"
    echo "5. 尝试使用 VPN"
fi

echo ""
echo "🎯 修复建议："
echo "如果问题持续存在，请尝试以下操作："
echo "1. 重启 Docker Desktop"
echo "2. 使用 make docker-build-fast 命令（使用缓存）"
echo "3. 使用 make docker-build-no-cache 命令（强制重新构建）"
echo "4. 检查 Docker Desktop 的网络设置"
echo ""
echo "完成诊断！"
